"""
模型管理业务逻辑服务

复用app.py中的模型管理和比较功能，提供API服务层
"""

import os
import sys
from typing import Dict, List, Optional, Any
import pandas as pd

# 添加父目录到路径以导入app.py中的函数
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入app.py中的核心功能
from app import (
    MODEL_INFOS, MODEL_PATHS, CLASSES, CURRENT_RUN_DIR,
    discover_available_models, get_model_info,
    create_pruning_comparison, create_distillation_comparison, create_quantization_comparison,
    read_evaluation_results, read_parameter_comparison
)

from ..models.model_info import (
    ModelMetrics, ModelVariantInfo, ModelTypeInfo, ModelComparisonData, 
    ComparisonMetric, ModelSummaryInfo
)

class ModelService:
    """模型管理服务类"""
    
    def __init__(self):
        """初始化模型服务"""
        self.initialize_models()
    
    def initialize_models(self):
        """初始化模型信息"""
        try:
            # 发现可用模型
            run_dir = discover_available_models()
            if run_dir:
                print(f"模型服务初始化成功，运行目录: {run_dir}")
            else:
                print("警告: 未找到有效的运行目录")
        except Exception as e:
            print(f"初始化模型服务时出错: {e}")
    
    def get_model_types(self) -> List[str]:
        """获取可用模型类型列表"""
        return list(MODEL_INFOS.keys())
    
    def get_model_info_by_type(self, model_type: str) -> Optional[ModelTypeInfo]:
        """获取指定模型类型的详细信息"""
        if model_type not in MODEL_INFOS:
            return None
        
        model_info = MODEL_INFOS[model_type]
        
        # 转换为API响应格式
        result = ModelTypeInfo(model_type=model_type)
        
        # 处理原始模型
        if model_info.get("original"):
            orig_info = model_info["original"]
            result.original = ModelVariantInfo(
                path=orig_info["path"],
                size_mb=orig_info["size_mb"],
                metrics=ModelMetrics(**orig_info["metrics"])
            )
        
        # 处理剪枝模型
        if model_info.get("pruned"):
            pruned_info = model_info["pruned"]
            result.pruned = ModelVariantInfo(
                path=pruned_info["path"],
                size_mb=pruned_info["size_mb"],
                metrics=ModelMetrics(**pruned_info["metrics"]),
                comparison=pruned_info.get("comparison")
            )
        
        # 处理蒸馏模型
        if model_info.get("distilled"):
            distilled_info = model_info["distilled"]
            result.distilled = ModelVariantInfo(
                path=distilled_info["path"],
                size_mb=distilled_info["size_mb"],
                metrics=ModelMetrics(**distilled_info["metrics"])
            )
        
        # 处理量化模型
        if model_info.get("quantized"):
            quantized_info = model_info["quantized"]
            result.quantized = ModelVariantInfo(
                path=quantized_info["path"],
                size_mb=quantized_info["size_mb"],
                metrics=ModelMetrics(**quantized_info["metrics"])
            )
        
        return result
    
    def get_models_summary(self) -> List[ModelSummaryInfo]:
        """获取所有模型的摘要信息"""
        summary_list = []
        
        for model_type in MODEL_INFOS.keys():
            model_info = MODEL_INFOS[model_type]
            
            variants_available = []
            has_original = bool(model_info.get("original"))
            has_pruned = bool(model_info.get("pruned"))
            has_distilled = bool(model_info.get("distilled"))
            has_quantized = bool(model_info.get("quantized"))
            
            if has_original:
                variants_available.append("original")
            if has_pruned:
                variants_available.append("pruned")
            if has_distilled:
                variants_available.append("distilled")
            if has_quantized:
                variants_available.append("quantized")
            
            summary = ModelSummaryInfo(
                model_type=model_type,
                variants_available=variants_available,
                total_variants=len(variants_available),
                has_original=has_original,
                has_pruned=has_pruned,
                has_distilled=has_distilled,
                has_quantized=has_quantized
            )
            summary_list.append(summary)
        
        return summary_list
    
    def _parse_html_table_to_comparison_data(self, html_content: str, comparison_type: str, model_type: str) -> Optional[ModelComparisonData]:
        """将HTML表格内容解析为比较数据"""
        try:
            # 如果是错误消息，直接返回None
            if "未找到" in html_content or "不存在" in html_content:
                return None
            
            # 使用pandas解析HTML表格
            tables = pd.read_html(html_content)
            if not tables:
                return None
            
            df = tables[0]
            
            # 根据比较类型确定模型名称
            if comparison_type == "pruning":
                original_name = f"{model_type} (原始)"
                optimized_name = f"{model_type} (剪枝)"
            elif comparison_type == "distillation":
                original_name = f"{model_type} (原始)"
                optimized_name = f"{model_type} (蒸馏 - MobileNetV2)"
            elif comparison_type == "quantization":
                original_name = f"{model_type} (原始)"
                optimized_name = f"{model_type} (量化)"
            else:
                return None
            
            # 解析表格数据
            metrics = []
            for _, row in df.iterrows():
                metric = ComparisonMetric(
                    metric_name=str(row.iloc[0]),
                    original_value=str(row.iloc[1]),
                    optimized_value=str(row.iloc[2]),
                    relative_change=str(row.iloc[3]) if len(row) > 3 else "N/A"
                )
                metrics.append(metric)
            
            return ModelComparisonData(
                comparison_type=comparison_type,
                model_type=model_type,
                original_model_name=original_name,
                optimized_model_name=optimized_name,
                metrics=metrics
            )
            
        except Exception as e:
            print(f"解析HTML表格时出错: {e}")
            return None
    
    def get_pruning_comparison(self, model_type: str) -> Optional[ModelComparisonData]:
        """获取剪枝模型比较数据"""
        try:
            html_content = create_pruning_comparison(model_type)
            return self._parse_html_table_to_comparison_data(html_content, "pruning", model_type)
        except Exception as e:
            print(f"获取剪枝比较数据时出错: {e}")
            return None
    
    def get_distillation_comparison(self, model_type: str) -> Optional[ModelComparisonData]:
        """获取蒸馏模型比较数据"""
        try:
            html_content = create_distillation_comparison(model_type)
            return self._parse_html_table_to_comparison_data(html_content, "distillation", model_type)
        except Exception as e:
            print(f"获取蒸馏比较数据时出错: {e}")
            return None
    
    def get_quantization_comparison(self, model_type: str) -> Optional[ModelComparisonData]:
        """获取量化模型比较数据"""
        try:
            if model_type != "resnet50":
                return None
            html_content = create_quantization_comparison(model_type)
            return self._parse_html_table_to_comparison_data(html_content, "quantization", model_type)
        except Exception as e:
            print(f"获取量化比较数据时出错: {e}")
            return None

# 全局模型服务实例
model_service = ModelService()
