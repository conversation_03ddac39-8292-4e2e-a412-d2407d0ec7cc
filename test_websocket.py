#!/usr/bin/env python3
"""
WebSocket连接测试脚本

测试WebSocket端点是否正常工作，验证连接、消息发送和接收功能
"""

import asyncio
import json
import websockets
import sys
from datetime import datetime

async def test_websocket_connection():
    """测试WebSocket连接"""
    uri = "ws://localhost:8000/ws"
    
    try:
        print(f"正在连接到 {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功建立")
            
            # 等待初始连接消息
            try:
                initial_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(initial_message)
                print(f"📨 收到初始消息: {data['type']} - {data.get('message', '')}")
            except asyncio.TimeoutError:
                print("⚠️  未收到初始连接消息")
            
            # 发送状态请求
            status_request = {
                "type": "status_request",
                "timestamp": datetime.now().timestamp()
            }
            await websocket.send(json.dumps(status_request))
            print("📤 已发送状态请求")
            
            # 等待状态响应
            try:
                status_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(status_response)
                print(f"📨 收到状态响应: {data['type']}")
                if data.get('payload'):
                    payload = data['payload']
                    print(f"   - 监控状态: {payload.get('monitoring', 'N/A')}")
                    print(f"   - 已加载模型: {payload.get('models_loaded', 'N/A')}")
                    print(f"   - 类别数量: {payload.get('classes_count', 'N/A')}")
            except asyncio.TimeoutError:
                print("⚠️  未收到状态响应")
            
            # 发送ping测试
            ping_message = {
                "type": "ping",
                "timestamp": datetime.now().timestamp()
            }
            await websocket.send(json.dumps(ping_message))
            print("📤 已发送ping消息")
            
            # 等待pong响应
            try:
                pong_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(pong_response)
                print(f"📨 收到pong响应: {data['type']}")
            except asyncio.TimeoutError:
                print("⚠️  未收到pong响应")
            
            # 保持连接一段时间以测试心跳
            print("⏳ 保持连接10秒以测试心跳...")
            try:
                for i in range(10):
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(message)
                    if data['type'] == 'heartbeat':
                        print(f"💓 收到心跳 ({i+1}/10)")
                    else:
                        print(f"📨 收到消息: {data['type']}")
            except asyncio.TimeoutError:
                print("⏰ 心跳测试超时")
            
            print("✅ WebSocket测试完成")
            
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 请确保服务器正在运行")
        return False
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ 连接失败 - 状态码: {e.status_code}")
        return False
    except OSError as e:
        print(f"❌ 网络连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False
    
    return True

async def test_multiple_connections():
    """测试多个WebSocket连接"""
    print("\n🔗 测试多个WebSocket连接...")
    
    uri = "ws://localhost:8000/ws"
    connections = []
    
    try:
        # 创建3个并发连接
        for i in range(3):
            websocket = await websockets.connect(uri)
            connections.append(websocket)
            print(f"✅ 连接 {i+1} 建立成功")
        
        # 向所有连接发送消息
        for i, websocket in enumerate(connections):
            message = {
                "type": "ping",
                "client_id": i+1,
                "timestamp": datetime.now().timestamp()
            }
            await websocket.send(json.dumps(message))
            print(f"📤 向连接 {i+1} 发送ping")
        
        # 接收响应
        for i, websocket in enumerate(connections):
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                data = json.loads(response)
                print(f"📨 连接 {i+1} 收到响应: {data['type']}")
            except asyncio.TimeoutError:
                print(f"⚠️  连接 {i+1} 响应超时")
        
        print("✅ 多连接测试完成")
        
    except Exception as e:
        print(f"❌ 多连接测试失败: {e}")
    finally:
        # 关闭所有连接
        for websocket in connections:
            await websocket.close()
        print("🔌 所有连接已关闭")

def print_usage():
    """打印使用说明"""
    print("WebSocket连接测试工具")
    print("=" * 50)
    print("使用方法:")
    print("  python test_websocket.py [选项]")
    print("")
    print("选项:")
    print("  --help, -h     显示此帮助信息")
    print("  --multi, -m    测试多个连接")
    print("")
    print("注意: 请确保Web应用服务器正在运行 (python src/web_app.py)")

async def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h']:
            print_usage()
            return
        elif sys.argv[1] in ['--multi', '-m']:
            success = await test_websocket_connection()
            if success:
                await test_multiple_connections()
            return
    
    # 默认单连接测试
    print("WebSocket连接测试")
    print("=" * 30)
    success = await test_websocket_connection()
    
    if success:
        print("\n🎉 所有测试通过！WebSocket功能正常")
    else:
        print("\n❌ 测试失败，请检查服务器状态")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        sys.exit(1)
