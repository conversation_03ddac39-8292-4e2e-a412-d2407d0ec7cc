"""
Remote Sensing Image Classification System
==========================================

A comprehensive system for remote sensing image classification with model optimization techniques
including pruning, quantization, and knowledge distillation.

Features:
- Multiple deep learning models (ResNet, DenseNet, ViT, Swin Transformer)
- Model optimization techniques (pruning, quantization, knowledge distillation)
- Web-based user interface with FastAPI and modern JavaScript
- Gradio interface for quick prototyping
- Comprehensive monitoring and logging
- Docker deployment support
- Extensive configuration management

Author: Remote Sensing Team
License: MIT
Version: 0.1.0
"""

import sys
import warnings
from pathlib import Path

# Version information
__version__ = "0.1.0"
__author__ = "Remote Sensing Team"
__email__ = "<EMAIL>"
__license__ = "MIT"
__description__ = "Remote Sensing Image Classification System with Model Optimization"

# Package metadata
__all__ = [
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    "__description__",
    "get_version",
    "get_package_info",
    "setup_logging",
    "check_dependencies",
]

# Minimum Python version check
MIN_PYTHON_VERSION = (3, 8)
if sys.version_info < MIN_PYTHON_VERSION:
    raise RuntimeError(
        f"Python {MIN_PYTHON_VERSION[0]}.{MIN_PYTHON_VERSION[1]} or higher is required. "
        f"You are using Python {sys.version_info.major}.{sys.version_info.minor}."
    )

# Package root directory
PACKAGE_ROOT = Path(__file__).parent
PROJECT_ROOT = PACKAGE_ROOT.parent

def get_version() -> str:
    """Get the package version."""
    return __version__

def get_package_info() -> dict:
    """Get comprehensive package information."""
    return {
        "name": "remote-sensing-classification",
        "version": __version__,
        "author": __author__,
        "email": __email__,
        "license": __license__,
        "description": __description__,
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        "package_root": str(PACKAGE_ROOT),
        "project_root": str(PROJECT_ROOT),
    }

def setup_logging(level: str = "INFO") -> None:
    """Setup basic logging configuration."""
    import logging

    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

def check_dependencies() -> dict:
    """Check if all required dependencies are available."""
    dependencies = {
        "torch": False,
        "torchvision": False,
        "numpy": False,
        "pandas": False,
        "sklearn": False,
        "matplotlib": False,
        "PIL": False,
        "fastapi": False,
        "gradio": False,
        "timm": False,
    }

    for dep in dependencies:
        try:
            if dep == "sklearn":
                import sklearn
            elif dep == "PIL":
                import PIL
            else:
                __import__(dep)
            dependencies[dep] = True
        except ImportError:
            dependencies[dep] = False

    return dependencies

# Package-level constants
SUPPORTED_IMAGE_FORMATS = [".jpg", ".jpeg", ".png", ".webp", ".bmp", ".tiff"]
SUPPORTED_MODEL_TYPES = ["resnet50", "densenet201", "vit_s_16", "swin_t"]
DEFAULT_IMAGE_SIZE = 224
DEFAULT_BATCH_SIZE = 32
DEFAULT_NUM_CLASSES = 21

# Setup warnings
warnings.filterwarnings("ignore", category=UserWarning, module="torch")
warnings.filterwarnings("ignore", category=FutureWarning, module="sklearn")

# Export commonly used items
try:
    from .config import config, get_config
    __all__.extend(["config", "get_config"])
except ImportError:
    pass