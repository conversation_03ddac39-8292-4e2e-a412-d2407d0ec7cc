<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>遥感图像分类系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛰️</text></svg>">
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-satellite"></i>
                    遥感图像分类系统
                </h1>
                <nav class="nav">
                    <button class="nav-btn active" data-tab="prediction">
                        <i class="fas fa-image"></i>
                        图像预测
                    </button>
                    <button class="nav-btn" data-tab="comparison">
                        <i class="fas fa-chart-bar"></i>
                        模型对比
                    </button>
                    <button class="nav-btn" data-tab="dataset">
                        <i class="fas fa-database"></i>
                        数据集
                    </button>
                    <button class="nav-btn" data-tab="adaptive">
                        <i class="fas fa-cogs"></i>
                        自适应微调
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Image Prediction Tab -->
            <section id="prediction" class="tab-content active">
                <div class="section-header">
                    <h2>图像分类预测</h2>
                    <p>上传遥感图像，选择模型进行场景分类</p>
                </div>
                
                <div class="prediction-container">
                    <div class="upload-section">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <h3>拖拽图像到此处或点击上传</h3>
                                <p>支持 JPG, PNG, JPEG 格式</p>
                                <input type="file" id="imageInput" accept="image/*" hidden>
                                <button class="btn btn-primary" onclick="document.getElementById('imageInput').click()">
                                    选择文件
                                </button>
                            </div>
                        </div>
                        
                        <div class="image-preview" id="imagePreview" style="display: none;">
                            <img id="previewImg" src="" alt="预览图像">
                            <button class="btn btn-secondary" onclick="clearImage()">
                                <i class="fas fa-times"></i>
                                清除
                            </button>
                        </div>
                    </div>

                    <div class="model-section">
                        <div class="model-selector">
                            <label for="modelSelect">选择模型:</label>
                            <select id="modelSelect" class="select">
                                <option value="">请选择模型...</option>
                                <option value="resnet50-原始">ResNet50 (原始)</option>
                                <option value="resnet50-剪枝">ResNet50 (剪枝)</option>
                                <option value="resnet50-蒸馏">ResNet50 (蒸馏)</option>
                                <option value="resnet50-量化">ResNet50 (量化)</option>
                                <option value="densenet201-原始">DenseNet201 (原始)</option>
                                <option value="vit_s_16-原始">ViT-S/16 (原始)</option>
                                <option value="swin_t-原始">Swin-T (原始)</option>
                            </select>
                        </div>

                        <button class="btn btn-primary btn-large" id="predictBtn" onclick="predictImage()" disabled>
                            <i class="fas fa-brain"></i>
                            开始预测
                        </button>
                    </div>
                </div>

                <div class="results-section" id="resultsSection" style="display: none;">
                    <h3>预测结果</h3>
                    <div class="results-container">
                        <div class="prediction-results" id="predictionResults">
                            <!-- Results will be populated here -->
                        </div>
                        <div class="inference-time" id="inferenceTime">
                            <!-- Inference time will be shown here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Model Comparison Tab -->
            <section id="comparison" class="tab-content">
                <div class="section-header">
                    <h2>模型性能对比</h2>
                    <p>比较不同优化技术的效果</p>
                </div>
                
                <div class="comparison-tabs">
                    <button class="comparison-tab-btn active" data-comparison="pruning">剪枝对比</button>
                    <button class="comparison-tab-btn" data-comparison="distillation">蒸馏对比</button>
                    <button class="comparison-tab-btn" data-comparison="quantization">量化对比</button>
                </div>

                <div class="comparison-content">
                    <div class="model-type-selector">
                        <label for="comparisonModelSelect">选择模型类型:</label>
                        <select id="comparisonModelSelect" class="select">
                            <option value="resnet50">ResNet50</option>
                            <option value="densenet201">DenseNet201</option>
                            <option value="vit_s_16">ViT-S/16</option>
                            <option value="swin_t">Swin-T</option>
                        </select>
                    </div>
                    
                    <div class="comparison-table-container" id="comparisonTableContainer">
                        <!-- Comparison tables will be loaded here -->
                    </div>
                </div>
            </section>

            <!-- Dataset Tab -->
            <section id="dataset" class="tab-content">
                <div class="section-header">
                    <h2>数据集可视化</h2>
                    <p>查看训练数据集和新数据集的样本</p>
                </div>
                
                <div class="dataset-container">
                    <div class="dataset-section">
                        <h3>旧数据集样本</h3>
                        <div class="dataset-images" id="oldDatasetImages">
                            <!-- Old dataset images will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="dataset-section">
                        <h3>新数据集样本</h3>
                        <div class="dataset-images" id="newDatasetImages">
                            <!-- New dataset images will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="refreshDatasetImages()">
                    <i class="fas fa-refresh"></i>
                    刷新样本
                </button>
            </section>

            <!-- Adaptive Fine-tuning Tab -->
            <section id="adaptive" class="tab-content">
                <div class="section-header">
                    <h2>自适应微调控制</h2>
                    <p>监控数据分布变化并自动触发模型微调</p>
                </div>
                
                <div class="adaptive-container">
                    <div class="control-panel">
                        <div class="control-group">
                            <label for="thresholdSlider">分布差异阈值:</label>
                            <div class="slider-container">
                                <input type="range" id="thresholdSlider" min="0.01" max="0.5" step="0.01" value="0.1">
                                <span id="thresholdValue">0.1</span>
                            </div>
                        </div>
                        
                        <div class="control-buttons">
                            <button class="btn btn-primary" onclick="startAdaptiveMonitoring()">
                                <i class="fas fa-play"></i>
                                启动自适应监控
                            </button>
                            <button class="btn btn-secondary" onclick="stopAdaptiveMonitoring()">
                                <i class="fas fa-stop"></i>
                                停止监控
                            </button>
                            <button class="btn btn-warning" onclick="manualFineTune()">
                                <i class="fas fa-wrench"></i>
                                手动触发微调
                            </button>
                            <button class="btn btn-info" onclick="checkDataDistribution()">
                                <i class="fas fa-search"></i>
                                检查数据分布
                            </button>
                        </div>
                    </div>
                    
                    <div class="status-panel">
                        <div class="status-item">
                            <h4>监控状态</h4>
                            <div class="status-indicator" id="monitoringStatus">
                                <span class="status-dot inactive"></span>
                                <span class="status-text">未启动</span>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <h4>微调状态</h4>
                            <div class="status-message" id="fineTuningStatus">
                                等待中...
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <h4>分布差异</h4>
                            <div class="distribution-info" id="distributionInfo">
                                未检测
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <p id="loadingText">处理中...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="/static/js/main.js"></script>
</body>
</html>
