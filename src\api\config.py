"""
FastAPI应用配置文件

包含应用的各种配置参数
"""

import os
from pathlib import Path
from typing import List

class Settings:
    """应用配置类"""
    
    # 应用基本信息
    APP_NAME: str = "遥感图像分类API"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "基于深度学习的遥感图像场景分类系统"
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
    ]
    
    # 文件路径配置
    PROJECT_ROOT: Path = Path(__file__).parent.parent.parent
    STATIC_DIR: Path = PROJECT_ROOT / "static"
    OUTPUTS_DIR: Path = PROJECT_ROOT / "outputs"
    IMGS_DIR: Path = PROJECT_ROOT / "imgs"
    OLD_DATASET_DIR: Path = PROJECT_ROOT / "old_dataset"
    NEW_DATASET_DIR: Path = PROJECT_ROOT / "new_dataset"
    
    # 模型配置
    CHECKPOINTS_DIR: Path = OUTPUTS_DIR / "checkpoints"
    MAX_IMAGE_SIZE: int = 10 * 1024 * 1024  # 10MB
    SUPPORTED_IMAGE_FORMATS: List[str] = [".jpg", ".jpeg", ".png"]
    
    # 自适应微调配置
    DISTRIBUTION_THRESHOLD: float = 0.1
    MONITORING_INTERVAL: int = 600  # 10分钟
    FINE_TUNE_EPOCHS: int = 5
    DISTILL_EPOCHS: int = 10
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    def __init__(self):
        """初始化配置，创建必要的目录"""
        self.create_directories()
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.STATIC_DIR,
            self.OUTPUTS_DIR,
            self.IMGS_DIR,
            self.CHECKPOINTS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

# 全局配置实例
settings = Settings()
