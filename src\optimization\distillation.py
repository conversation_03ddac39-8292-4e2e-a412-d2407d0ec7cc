import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from typing import Dict, List, Tuple, Optional, Union, Callable
import copy
import time
from tqdm import tqdm

from models.base_model import ResNet50Model


class DistillationLoss(nn.Module):
    """知识蒸馏损失函数"""
    
    def __init__(self, alpha: float = 0.5, temp: float = 4.0):
        """
        初始化蒸馏损失
        
        Args:
            alpha: 蒸馏损失权重，在[0, 1]范围内
            temp: 温度系数，控制软标签的软化程度
        """
        super(DistillationLoss, self).__init__()
        self.alpha = alpha
        self.temp = temp
        self.criterion = nn.CrossEntropyLoss()
    
    def forward(
        self, 
        outputs: torch.Tensor, 
        labels: torch.Tensor, 
        teacher_outputs: torch.Tensor
    ) -> torch.Tensor:
        """
        计算蒸馏损失
        
        Args:
            outputs: 学生模型输出
            labels: 真实标签
            teacher_outputs: 教师模型输出
            
        Returns:
            蒸馏损失值
        """
        # 硬目标损失 (传统交叉熵)
        hard_loss = self.criterion(outputs, labels)
        
        # 软目标损失 (KL散度)
        soft_loss = F.kl_div(
            F.log_softmax(outputs / self.temp, dim=1),
            F.softmax(teacher_outputs / self.temp, dim=1),
            reduction='batchmean'
        ) * (self.temp * self.temp)
        
        # 综合损失
        loss = (1 - self.alpha) * hard_loss + self.alpha * soft_loss
        
        return loss


class ResNet18Student(nn.Module):
    """使用ResNet18作为学生模型"""
    
    def __init__(self, num_classes: int, pretrained: bool = True):
        """
        初始化ResNet18学生模型
        
        Args:
            num_classes: 分类类别数量
            pretrained: 是否使用预训练权重
        """
        super(ResNet18Student, self).__init__()
        
        # 加载预训练的ResNet18模型
        from torchvision.models import resnet18
        self.model = resnet18(pretrained=pretrained)
        
        # 修改最后的全连接层以适应类别数
        in_features = self.model.fc.in_features
        self.model.fc = nn.Linear(in_features, num_classes)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return self.model(x)


class MobileNetV2Student(nn.Module):
    """使用MobileNetV2作为学生模型"""
    
    def __init__(self, num_classes: int, pretrained: bool = True):
        """
        初始化MobileNetV2学生模型
        
        Args:
            num_classes: 分类类别数量
            pretrained: 是否使用预训练权重
        """
        super(MobileNetV2Student, self).__init__()
        
        # 加载预训练的MobileNetV2模型
        from torchvision.models import mobilenet_v2
        self.model = mobilenet_v2(pretrained=pretrained)
        
        # 修改分类器以适应类别数
        in_features = self.model.classifier[1].in_features
        self.model.classifier[1] = nn.Linear(in_features, num_classes)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return self.model(x)


def train_with_distillation(
    teacher_model: nn.Module,
    student_model: nn.Module,
    train_loader: DataLoader,
    val_loader: DataLoader,
    device: torch.device,
    criterion: nn.Module,
    optimizer: Optional[torch.optim.Optimizer] = None,
    scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
    num_epochs: int = 10,
    alpha: float = 0.5,
    temperature: float = 4.0,
    patience: int = 5,
    learning_rate: float = 0.001
) -> nn.Module:
    """
    使用知识蒸馏训练学生模型
    
    Args:
        teacher_model: 教师模型
        student_model: 学生模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        device: 计算设备
        criterion: 损失函数
        optimizer: 优化器，如果为None则创建新的优化器
        scheduler: 学习率调度器
        num_epochs: 训练轮数
        alpha: 蒸馏损失权重
        temperature: 温度系数
        patience: 早停耐心值
        learning_rate: 学习率，当optimizer为None时使用
        
    Returns:
        训练好的学生模型
    """
    # 确保教师模型处于评估模式
    teacher_model.eval()
    
    # 如果未提供optimizer，创建一个
    if optimizer is None:
        optimizer = torch.optim.Adam(student_model.parameters(), lr=learning_rate)
    
    # 蒸馏损失
    distill_criterion = DistillationLoss(alpha, temperature)
    
    # 记录训练过程
    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []
    
    # 记录最佳模型
    best_val_acc = 0.0
    best_model_weights = copy.deepcopy(student_model.state_dict())
    no_improve_epochs = 0
    
    for epoch in range(num_epochs):
        print(f"Epoch {epoch+1}/{num_epochs}")
        print('-' * 10)
        
        # 训练阶段
        student_model.train()
        running_loss = 0.0
        running_corrects = 0
        processed_size = 0
        
        for inputs, labels in tqdm(train_loader, desc=f"Training Epoch {epoch+1}"):
            inputs, labels = inputs.to(device), labels.to(device)
            
            # 清零梯度
            optimizer.zero_grad()
            
            # 前向传播
            with torch.no_grad():
                teacher_outputs = teacher_model(inputs)
            
            student_outputs = student_model(inputs)
            
            # 计算损失
            loss = distill_criterion(student_outputs, labels, teacher_outputs)
            
            # 反向传播和优化
            loss.backward()
            optimizer.step()
            
            # 统计
            running_loss += loss.item() * inputs.size(0)
            _, preds = torch.max(student_outputs, 1)
            running_corrects += torch.sum(preds == labels.data).item()
            processed_size += inputs.size(0)
        
        # 计算训练指标
        epoch_loss = running_loss / processed_size
        epoch_acc = running_corrects / processed_size
        train_losses.append(epoch_loss)
        train_accs.append(epoch_acc)
        
        print(f"Train Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}")
        
        # 验证阶段
        student_model.eval()
        running_loss = 0.0
        running_corrects = 0
        processed_size = 0
        
        with torch.no_grad():
            for inputs, labels in tqdm(val_loader, desc=f"Validating Epoch {epoch+1}"):
                inputs, labels = inputs.to(device), labels.to(device)
                
                # 前向传播
                student_outputs = student_model(inputs)
                
                # 计算损失 (仅使用传统损失函数)
                loss = criterion(student_outputs, labels)
                
                # 统计
                running_loss += loss.item() * inputs.size(0)
                _, preds = torch.max(student_outputs, 1)
                running_corrects += torch.sum(preds == labels.data).item()
                processed_size += inputs.size(0)
        
        # 计算验证指标
        epoch_loss = running_loss / processed_size
        epoch_acc = running_corrects / processed_size
        val_losses.append(epoch_loss)
        val_accs.append(epoch_acc)
        
        print(f"Val Loss: {epoch_loss:.4f} Acc: {epoch_acc:.4f}")
        
        # 学习率调整
        if scheduler:
            scheduler.step()
            print(f"Learning Rate: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if epoch_acc > best_val_acc:
            best_val_acc = epoch_acc
            best_model_weights = copy.deepcopy(student_model.state_dict())
            no_improve_epochs = 0
            print(f"New best validation accuracy: {best_val_acc:.4f}")
        else:
            no_improve_epochs += 1
        
        # 早停
        if no_improve_epochs >= patience:
            print(f"Early stopping triggered after {epoch+1} epochs")
            break
        
        print()
    
    # 加载最佳模型权重
    student_model.load_state_dict(best_model_weights)
    
    return student_model


def create_student_model(
    model_type: str, 
    num_classes: int, 
    pretrained: bool = True
) -> nn.Module:
    """
    创建学生模型
    
    Args:
        model_type: 模型类型，'resnet18' 或 'mobilenetv2'
        num_classes: 类别数量
        pretrained: 是否使用预训练权重
        
    Returns:
        学生模型
    """
    if model_type.lower() == 'resnet18':
        return ResNet18Student(num_classes, pretrained)
    elif model_type.lower() == 'mobilenetv2':
        return MobileNetV2Student(num_classes, pretrained)
    else:
        raise ValueError(f"Unsupported model type: {model_type}")


def compare_models_performance(
    teacher_model: nn.Module,
    student_model: nn.Module,
    test_loader: DataLoader,
    device: torch.device
) -> Dict[str, float]:
    """
    比较教师模型和学生模型的性能
    
    Args:
        teacher_model: 教师模型
        student_model: 学生模型
        test_loader: 测试数据加载器
        device: 计算设备
        
    Returns:
        性能比较结果
    """
    # 确保模型处于评估模式
    teacher_model.eval()
    student_model.eval()
    
    # 准确率
    teacher_correct = 0
    student_correct = 0
    total = 0
    
    # 推理时间
    teacher_time = 0.0
    student_time = 0.0
    
    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            batch_size = inputs.size(0)
            
            # 教师模型推理
            start_time = time.time()
            teacher_outputs = teacher_model(inputs)
            torch.cuda.synchronize()
            teacher_time += time.time() - start_time
            
            # 学生模型推理
            start_time = time.time()
            student_outputs = student_model(inputs)
            torch.cuda.synchronize()
            student_time += time.time() - start_time
            
            # 计算准确率
            _, teacher_preds = torch.max(teacher_outputs, 1)
            _, student_preds = torch.max(student_outputs, 1)
            
            teacher_correct += torch.sum(teacher_preds == labels).item()
            student_correct += torch.sum(student_preds == labels).item()
            total += batch_size
    
    # 参数数量
    teacher_params = sum(p.numel() for p in teacher_model.parameters() if p.requires_grad)
    student_params = sum(p.numel() for p in student_model.parameters() if p.requires_grad)
    
    # 计算结果
    results = {
        "teacher_accuracy": teacher_correct / total,
        "student_accuracy": student_correct / total,
        "accuracy_diff": (student_correct - teacher_correct) / total,
        "relative_accuracy": student_correct / teacher_correct,
        "teacher_params": teacher_params,
        "student_params": student_params,
        "params_reduction": 1 - (student_params / teacher_params),
        "teacher_inference_time": teacher_time,
        "student_inference_time": student_time,
        "speedup": teacher_time / student_time if student_time > 0 else float('inf')
    }
    
    return results


if __name__ == "__main__":
    # 测试代码
    import torch.optim as optim
    from torch.utils.data import TensorDataset, DataLoader
    
    # 创建示例模型
    num_classes = 10
    teacher_model = ResNet50Model(num_classes=num_classes, pretrained=True)
    student_model = create_student_model('resnet18', num_classes=num_classes, pretrained=True)
    
    # 创建示例数据
    x = torch.randn(100, 3, 224, 224)
    y = torch.randint(0, num_classes, (100,))
    dataset = TensorDataset(x, y)
    loader = DataLoader(dataset, batch_size=16, shuffle=True)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    teacher_model = teacher_model.to(device)
    student_model = student_model.to(device)
    
    # 打印模型参数数量
    teacher_params = sum(p.numel() for p in teacher_model.parameters() if p.requires_grad)
    student_params = sum(p.numel() for p in student_model.parameters() if p.requires_grad)
    
    print(f"教师模型参数量: {teacher_params:,}")
    print(f"学生模型参数量: {student_params:,}")
    print(f"参数减少比例: {(1 - student_params/teacher_params)*100:.2f}%") 