/*
 * Flat Design CSS for Remote Sensing Image Classification System
 * Pure flat design implementation with no shadows, gradients, or 3D effects
 */

/* CSS Variables for Flat Design Color Palette */
:root {
    --primary-color: #3498db;      /* Bright Blue */
    --primary-dark: #2980b9;       /* Darker Blue */
    --success-color: #2ecc71;      /* Bright Green */
    --success-dark: #27ae60;       /* Darker Green */
    --warning-color: #f39c12;      /* Bright Orange */
    --warning-dark: #e67e22;       /* Darker Orange */
    --danger-color: #e74c3c;       /* Bright Red */
    --danger-dark: #c0392b;        /* Darker Red */
    --secondary-color: #95a5a6;    /* Gray */
    --secondary-dark: #7f8c8d;     /* Darker Gray */
    --dark-color: #2c3e50;         /* Dark Blue-Gray */
    --light-color: #ecf0f1;        /* Light Gray */
    --white-color: #ffffff;        /* Pure White */
    --text-color: #2c3e50;         /* Dark Text */
    --text-muted: #7f8c8d;         /* Muted Text */
    --border-color: #bdc3c7;       /* Border Gray */
    --background-color: #ecf0f1;   /* Light Background */
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    font-weight: 400;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles - Flat Design */
.header {
    background: var(--white-color);
    border-bottom: 3px solid var(--primary-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.logo i {
    color: var(--primary-color);
    font-size: 2rem;
}

.nav {
    display: flex;
    gap: 0;
    background: var(--light-color);
    border-radius: 0;
    border: 2px solid var(--border-color);
}

.nav-btn {
    background: transparent;
    border: none;
    border-right: 2px solid var(--border-color);
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-btn:last-child {
    border-right: none;
}

.nav-btn:hover {
    background: var(--white-color);
    color: var(--primary-color);
}

.nav-btn.active {
    background: var(--primary-color);
    color: var(--white-color);
}

/* Main Content - Flat Design */
.main {
    padding: 3rem 0;
    background: var(--background-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--white-color);
    border: 3px solid var(--primary-color);
    border-radius: 0;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.section-header p {
    color: var(--text-muted);
    font-size: 1.2rem;
    font-weight: 500;
}

/* Button Styles - Pure Flat Design */
.btn {
    background: var(--primary-color);
    color: var(--white-color);
    border: 3px solid var(--primary-color);
    padding: 1rem 2rem;
    border-radius: 0;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: background-color 0.2s ease, color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    text-decoration: none;
    min-width: 140px;
}

.btn:hover {
    background: var(--white-color);
    color: var(--primary-color);
}

.btn:active {
    background: var(--primary-dark);
    color: var(--white-color);
    border-color: var(--primary-dark);
}

.btn-secondary {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background: var(--white-color);
    color: var(--secondary-color);
}

.btn-warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: var(--white-color);
    color: var(--warning-color);
}

.btn-info {
    background: var(--success-color);
    border-color: var(--success-color);
}

.btn-info:hover {
    background: var(--white-color);
    color: var(--success-color);
}

.btn-large {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    min-width: 180px;
}

.btn:disabled {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Form Elements - Flat Design */
.select {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 3px solid var(--border-color);
    border-radius: 0;
    font-size: 1rem;
    font-weight: 600;
    background: var(--white-color);
    color: var(--text-color);
    transition: border-color 0.2s ease;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23bdc3c7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1rem;
    padding-right: 3rem;
}

.select:focus {
    outline: none;
    border-color: var(--primary-color);
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* Upload Section - Flat Design */
.prediction-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.upload-area {
    border: 4px dashed var(--border-color);
    border-radius: 0;
    padding: 3rem 2rem;
    text-align: center;
    transition: border-color 0.2s ease, background-color 0.2s ease;
    background: var(--white-color);
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--light-color);
}

.upload-area.dragover {
    border-color: var(--success-color);
    background: var(--light-color);
    border-style: solid;
}

.upload-content i {
    font-size: 4rem;
    color: var(--border-color);
    margin-bottom: 1.5rem;
}

.upload-area:hover .upload-content i {
    color: var(--primary-color);
}

.upload-content h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
    font-weight: 700;
    font-size: 1.3rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.upload-content p {
    color: var(--text-muted);
    margin-bottom: 2rem;
    font-size: 1.1rem;
    font-weight: 500;
}

.image-preview {
    position: relative;
    background: var(--white-color);
    border: 3px solid var(--success-color);
    border-radius: 0;
    padding: 1.5rem;
}

.image-preview img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 0;
    margin-bottom: 1.5rem;
    border: 2px solid var(--border-color);
}

.model-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    background: var(--white-color);
    padding: 2rem;
    border: 3px solid var(--border-color);
}

.model-selector label {
    display: block;
    margin-bottom: 1rem;
    font-weight: 700;
    color: var(--dark-color);
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Results Section - Enhanced Flat Design */
.results-section {
    background: var(--white-color);
    border: 3px solid var(--success-color);
    border-radius: 0;
    padding: 3rem;
    margin-top: 3rem;
    animation: slideInUp 0.5s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.results-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.prediction-results {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.result-item {
    display: flex;
    flex-direction: column;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: 0;
    border-left: 6px solid var(--primary-color);
    border-right: 2px solid var(--border-color);
    border-top: 2px solid var(--border-color);
    border-bottom: 2px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
    animation: fadeInLeft 0.5s ease;
    animation-fill-mode: both;
}

.result-item:nth-child(1) { animation-delay: 0.1s; }
.result-item:nth-child(2) { animation-delay: 0.2s; }
.result-item:nth-child(3) { animation-delay: 0.3s; }
.result-item:nth-child(4) { animation-delay: 0.4s; }
.result-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.result-item:hover {
    background: var(--white-color);
    border-left-color: var(--success-color);
    transform: translateX(5px);
}

.result-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.result-label {
    font-weight: 700;
    color: var(--dark-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 1.1rem;
}

.result-confidence {
    font-weight: 700;
    color: var(--success-color);
    font-size: 1.3rem;
    background: var(--white-color);
    padding: 0.5rem 1rem;
    border: 2px solid var(--success-color);
}

.confidence-bar-container {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.confidence-bar {
    height: 100%;
    background: var(--success-color);
    transition: width 1s ease;
    animation: fillBar 1.5s ease;
}

@keyframes fillBar {
    from { width: 0%; }
}

.inference-time {
    background: var(--success-color);
    color: var(--white-color);
    padding: 2rem;
    border-radius: 0;
    text-align: center;
    font-weight: 700;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 3px solid var(--success-dark);
    animation: fadeInRight 0.5s ease 0.3s both;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.inference-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.inference-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.export-results-btn {
    margin-top: 2rem;
    align-self: flex-start;
}

/* Comparison Section - Flat Design */
.comparison-tabs {
    display: flex;
    gap: 0;
    margin-bottom: 3rem;
    background: var(--light-color);
    padding: 0;
    border-radius: 0;
    border: 3px solid var(--border-color);
}

.comparison-tab-btn {
    flex: 1;
    background: transparent;
    border: none;
    border-right: 3px solid var(--border-color);
    padding: 1.5rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.comparison-tab-btn:last-child {
    border-right: none;
}

.comparison-tab-btn:hover {
    background: var(--white-color);
    color: var(--primary-color);
}

.comparison-tab-btn.active {
    background: var(--primary-color);
    color: var(--white-color);
}

.comparison-content {
    background: var(--white-color);
    border: 3px solid var(--border-color);
    border-radius: 0;
    padding: 3rem;
}

.model-type-selector {
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--light-color);
    border: 2px solid var(--border-color);
}

.model-type-selector label {
    display: block;
    margin-bottom: 1rem;
    font-weight: 700;
    color: var(--dark-color);
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Dataset Section - Flat Design */
.dataset-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.dataset-section {
    background: var(--white-color);
    border: 3px solid var(--warning-color);
    border-radius: 0;
    padding: 2rem;
}

.dataset-section h3 {
    margin-bottom: 2rem;
    color: var(--dark-color);
    text-align: center;
    font-weight: 700;
    font-size: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    padding: 1rem;
    background: var(--warning-color);
    color: var(--white-color);
    margin: -2rem -2rem 2rem -2rem;
}

.dataset-images {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.dataset-image {
    position: relative;
    border: 3px solid var(--border-color);
    border-radius: 0;
    overflow: hidden;
    background: var(--light-color);
}

.dataset-image img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
}

.dataset-image-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--dark-color);
    color: var(--white-color);
    padding: 0.75rem;
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Adaptive Section - Flat Design */
.adaptive-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.control-panel {
    background: var(--white-color);
    border: 3px solid var(--success-color);
    border-radius: 0;
    padding: 3rem;
}

.status-panel {
    background: var(--white-color);
    border: 3px solid var(--warning-color);
    border-radius: 0;
    padding: 3rem;
}

.control-group {
    margin-bottom: 3rem;
}

.control-group label {
    display: block;
    margin-bottom: 1rem;
    font-weight: 700;
    color: var(--dark-color);
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1rem;
    background: var(--light-color);
    border: 2px solid var(--border-color);
}

.slider-container input[type="range"] {
    flex: 1;
    height: 8px;
    border-radius: 0;
    background: var(--border-color);
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 0;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid var(--primary-dark);
}

.slider-container span {
    font-weight: 700;
    color: var(--primary-color);
    min-width: 60px;
    font-size: 1.2rem;
    background: var(--white-color);
    padding: 0.5rem 1rem;
    border: 2px solid var(--primary-color);
}

.control-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.status-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--light-color);
    border: 2px solid var(--border-color);
}

.status-item h4 {
    margin-bottom: 1rem;
    color: var(--dark-color);
    font-weight: 700;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-dot {
    width: 16px;
    height: 16px;
    border-radius: 0;
    background: var(--secondary-color);
    border: 2px solid var(--secondary-dark);
}

.status-dot.active {
    background: var(--success-color);
    border-color: var(--success-dark);
    animation: pulse 2s infinite;
}

.status-dot.inactive {
    background: var(--danger-color);
    border-color: var(--danger-dark);
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.status-text {
    font-weight: 700;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-message, .distribution-info {
    padding: 1.5rem;
    background: var(--white-color);
    border-radius: 0;
    border: 3px solid var(--primary-color);
    border-left: 6px solid var(--primary-color);
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
}

/* Loading Overlay - Flat Design */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(44, 62, 80, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    background: var(--white-color);
    padding: 4rem;
    border: 4px solid var(--primary-color);
    border-radius: 0;
    text-align: center;
    min-width: 300px;
}

.loading-content p {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--dark-color);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 2rem;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 6px solid var(--light-color);
    border-top: 6px solid var(--primary-color);
    border-right: 6px solid var(--primary-color);
    border-radius: 0;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications - Flat Design */
.toast-container {
    position: fixed;
    top: 30px;
    right: 30px;
    z-index: 1001;
}

.toast {
    background: var(--white-color);
    border: 3px solid var(--primary-color);
    border-radius: 0;
    padding: 1.5rem 2rem;
    margin-bottom: 1rem;
    border-left: 6px solid var(--primary-color);
    animation: slideIn 0.3s ease;
    min-width: 300px;
    font-weight: 600;
    font-size: 1rem;
    color: var(--dark-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.toast.success {
    border-color: var(--success-color);
    border-left-color: var(--success-color);
}

.toast.error {
    border-color: var(--danger-color);
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-color: var(--warning-color);
    border-left-color: var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Additional Flat Design Elements */
.pure-table {
    border-collapse: collapse;
    width: 100%;
    font-size: 1rem;
    margin-bottom: 2rem;
    border: 3px solid var(--border-color);
}

.pure-table th, .pure-table td {
    padding: 1rem 1.5rem;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    border-right: 2px solid var(--border-color);
    font-weight: 600;
}

.pure-table th {
    background: var(--primary-color);
    color: var(--white-color);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.pure-table-striped tr:nth-child(even) {
    background: var(--light-color);
}

.pure-table-striped tr:nth-child(odd) {
    background: var(--white-color);
}

/* Responsive Design - Flat Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        padding: 2rem 0;
    }

    .nav {
        width: 100%;
        flex-direction: column;
        border: 3px solid var(--border-color);
    }

    .nav-btn {
        border-right: none;
        border-bottom: 2px solid var(--border-color);
        padding: 1.5rem;
        text-align: center;
    }

    .nav-btn:last-child {
        border-bottom: none;
    }

    .prediction-container,
    .dataset-container,
    .adaptive-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .results-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .control-buttons {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .comparison-tabs {
        flex-direction: column;
        border: 3px solid var(--border-color);
    }

    .comparison-tab-btn {
        border-right: none;
        border-bottom: 2px solid var(--border-color);
    }

    .comparison-tab-btn:last-child {
        border-bottom: none;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .logo {
        font-size: 1.5rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .dataset-images {
        grid-template-columns: 1fr;
    }
}

/* Enhanced UI Components */
.image-metadata {
    background: var(--light-color);
    border: 2px solid var(--border-color);
    padding: 1rem;
    margin-top: 1rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.metadata-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: var(--white-color);
    border: 1px solid var(--border-color);
}

.metadata-label {
    font-weight: 700;
    color: var(--text-muted);
    text-transform: uppercase;
    font-size: 0.9rem;
}

.metadata-value {
    font-weight: 600;
    color: var(--dark-color);
}

.model-info {
    background: var(--light-color);
    border: 2px solid var(--primary-color);
    padding: 1.5rem;
    margin-top: 1rem;
    display: none;
}

.model-info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--white-color);
    border: 1px solid var(--border-color);
    margin-bottom: 0.5rem;
}

.info-label {
    font-weight: 700;
    color: var(--text-muted);
    text-transform: uppercase;
    font-size: 0.9rem;
}

.info-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Upload Progress */
.upload-progress {
    background: var(--white-color);
    border: 2px solid var(--primary-color);
    padding: 1rem;
    margin: 1rem 0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.progress-filename {
    font-weight: 600;
    color: var(--dark-color);
}

.progress-percentage {
    font-weight: 700;
    color: var(--primary-color);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border: 1px solid var(--border-color);
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-status {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 600;
}

/* Enhanced Dataset Images */
.dataset-image {
    position: relative;
    background: var(--white-color);
    border: 2px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.dataset-image:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.dataset-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.dataset-image:hover img {
    transform: scale(1.05);
}

.dataset-image-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--dark-color);
    color: var(--white-color);
    padding: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.image-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dataset-image:hover .image-actions {
    opacity: 1;
}

.btn-icon {
    background: var(--white-color);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-icon:hover {
    background: var(--primary-color);
    color: var(--white-color);
}

.image-error {
    background: var(--danger-color);
    color: var(--white-color);
}

.image-error img {
    display: none;
}

.image-error::after {
    content: "图像加载失败";
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    font-weight: 700;
    text-transform: uppercase;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(44, 62, 80, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--white-color);
    border: 3px solid var(--primary-color);
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
    min-width: 400px;
}

.modal-header {
    background: var(--primary-color);
    color: var(--white-color);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.modal-close {
    background: none;
    border: none;
    color: var(--white-color);
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    background: var(--light-color);
    padding: 1.5rem;
    border-top: 2px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--dark-color);
    color: var(--white-color);
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
    border: 2px solid var(--white-color);
    font-weight: 700;
    text-transform: uppercase;
}

.skip-link:focus {
    top: 6px;
}

/* Loading States */
.loading .btn {
    pointer-events: none;
    opacity: 0.6;
}

.mobile-layout .prediction-container {
    grid-template-columns: 1fr;
}

/* Print Styles - Flat Design */
@media print {
    .header, .nav, .btn, .loading-overlay, .toast-container {
        display: none !important;
    }

    .main {
        padding: 0;
    }

    .section-header {
        border: 2px solid #000;
        margin-bottom: 2rem;
    }

    .results-section, .comparison-content, .dataset-section {
        border: 2px solid #000;
        margin-bottom: 2rem;
        break-inside: avoid;
    }
}
