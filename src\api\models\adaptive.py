"""
自适应微调相关的Pydantic模型定义

包含自适应微调请求和响应的数据模型
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field

class AdaptiveStatusResponse(BaseModel):
    """自适应监控状态响应"""
    success: bool = Field(..., description="请求是否成功")
    monitoring_active: bool = Field(..., description="监控是否激活")
    fine_tuning_running: bool = Field(..., description="微调是否正在运行")
    fine_tuning_message: str = Field(..., description="微调状态消息")
    last_check_time: Optional[str] = Field(None, description="上次检查时间")
    distribution_threshold: float = Field(..., description="分布差异阈值")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")

class AdaptiveControlResponse(BaseModel):
    """自适应控制响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")
    error: Optional[str] = Field(None, description="错误信息")

class DistributionCheckRequest(BaseModel):
    """分布检查请求"""
    threshold: Optional[float] = Field(None, description="自定义阈值，不提供则使用默认值")

class DistributionCheckResponse(BaseModel):
    """分布检查响应"""
    success: bool = Field(..., description="检查是否成功")
    needs_fine_tuning: bool = Field(..., description="是否需要微调")
    difference_score: float = Field(..., description="分布差异分数")
    threshold: float = Field(..., description="使用的阈值")
    old_features_count: Optional[int] = Field(None, description="旧数据特征数量")
    new_features_count: Optional[int] = Field(None, description="新数据特征数量")
    message: str = Field(..., description="检查结果消息")
    error: Optional[str] = Field(None, description="错误信息")

class ManualTuneRequest(BaseModel):
    """手动微调请求"""
    model_type: Optional[str] = Field("resnet50", description="要微调的模型类型")
    epochs: Optional[int] = Field(5, description="微调轮数")
    learning_rate: Optional[float] = Field(1e-4, description="学习率")

class ManualTuneResponse(BaseModel):
    """手动微调响应"""
    success: bool = Field(..., description="微调启动是否成功")
    message: str = Field(..., description="微调状态消息")
    error: Optional[str] = Field(None, description="错误信息")

class ThresholdUpdateRequest(BaseModel):
    """阈值更新请求"""
    threshold: float = Field(..., ge=0.01, le=0.5, description="新的分布差异阈值(0.01-0.5)")

class ThresholdUpdateResponse(BaseModel):
    """阈值更新响应"""
    success: bool = Field(..., description="更新是否成功")
    old_threshold: float = Field(..., description="旧阈值")
    new_threshold: float = Field(..., description="新阈值")
    message: str = Field(..., description="更新结果消息")
    error: Optional[str] = Field(None, description="错误信息")

class AdaptiveConfigResponse(BaseModel):
    """自适应配置响应"""
    success: bool = Field(..., description="请求是否成功")
    config: Dict[str, Any] = Field(..., description="当前配置")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")
