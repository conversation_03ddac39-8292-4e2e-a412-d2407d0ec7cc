import os
import argparse
import time
from typing import Dict, Tuple
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm

from data.dataset import create_dataloaders
from models.base_model import create_model, get_model_info
from optimization.pruning import (
    prune_model_global, prune_model_l1_structured, prune_model_l1_unstructured,
    compare_models, calculate_sparsity, iterative_pruning
)
from utils.metrics import evaluate_model, count_parameters
from utils.visualization import plot_model_comparison


def fine_tune(
    model: nn.Module,
    train_loader: DataLoader,
    val_loader: DataLoader,
    device: torch.device,
    epochs: int = 5,
    lr: float = 0.001
) -> nn.Module:
    """
    对剪枝后的模型进行微调
    
    Args:
        model: 剪枝后的模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        device: 计算设备
        epochs: 微调轮数
        lr: 学习率
        
    Returns:
        微调后的模型
    """
    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    
    # 训练模式
    model.train()
    
    # 记录最佳模型
    best_val_acc = 0.0
    best_model_state = model.state_dict().copy()
    
    # 微调训练
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for inputs, labels in tqdm(train_loader, desc=f"Fine-tuning epoch {epoch+1}/{epochs}"):
            inputs, labels = inputs.to(device), labels.to(device)
            
            # 清零梯度
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(inputs)
            
            # 计算损失
            loss = criterion(outputs, labels)
            
            # 反向传播
            loss.backward()
            
            # 参数更新
            optimizer.step()
            
            # 统计
            train_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            train_correct += (predicted == labels).sum().item()
            train_total += labels.size(0)
        
        # 计算训练指标
        train_epoch_loss = train_loss / train_total
        train_epoch_acc = train_correct / train_total
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for inputs, labels in tqdm(val_loader, desc=f"Validating epoch {epoch+1}/{epochs}"):
                inputs, labels = inputs.to(device), labels.to(device)
                
                # 前向传播
                outputs = model(inputs)
                
                # 计算损失
                loss = criterion(outputs, labels)
                
                # 统计
                val_loss += loss.item() * inputs.size(0)
                _, predicted = torch.max(outputs, 1)
                val_correct += (predicted == labels).sum().item()
                val_total += labels.size(0)
        
        # 计算验证指标
        val_epoch_loss = val_loss / val_total
        val_epoch_acc = val_correct / val_total
        
        print(f"Epoch {epoch+1}/{epochs}:")
        print(f"  Train Loss: {train_epoch_loss:.4f}, Train Acc: {train_epoch_acc:.4f}")
        print(f"  Val Loss: {val_epoch_loss:.4f}, Val Acc: {val_epoch_acc:.4f}")
        
        # 保存最佳模型
        if val_epoch_acc > best_val_acc:
            best_val_acc = val_epoch_acc
            best_model_state = model.state_dict().copy()
            print(f"  New best validation accuracy: {best_val_acc:.4f}")
    
    # 恢复最佳模型
    model.load_state_dict(best_model_state)
    
    return model


def prune_and_evaluate(
    model: nn.Module,
    train_loader: DataLoader,
    val_loader: DataLoader,
    test_loader: DataLoader,
    classes: list,
    device: torch.device,
    pruning_method: str,
    pruning_ratio: float,
    fine_tune_epochs: int,
    output_dir: str
) -> Tuple[nn.Module, Dict]:
    """
    剪枝模型并评估性能
    
    Args:
        model: 原始模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        test_loader: 测试数据加载器
        classes: 类别列表
        device: 计算设备
        pruning_method: 剪枝方法，可选'global', 'structured', 'unstructured'
        pruning_ratio: 剪枝比例
        fine_tune_epochs: 微调轮数
        output_dir: 输出目录
        
    Returns:
        (剪枝后的模型, 评估结果)
    """
    print(f"正在使用 {pruning_method} 方法进行剪枝，比例为 {pruning_ratio}...")
    
    # 选择剪枝方法
    if pruning_method == 'global':
        pruned_model = prune_model_global(model, pruning_ratio)
    elif pruning_method == 'structured':
        pruned_model = prune_model_l1_structured(model, pruning_ratio)
    elif pruning_method == 'unstructured':
        pruned_model = prune_model_l1_unstructured(model, pruning_ratio)
    else:
        raise ValueError(f"不支持的剪枝方法: {pruning_method}")
    
    # 计算剪枝后的稀疏度
    sparsity = calculate_sparsity(pruned_model)
    print(f"剪枝后的稀疏度: {sparsity:.4f}")
    
    # 比较原始模型和剪枝后的模型
    comparison = compare_models(model, pruned_model)
    print(f"参数减少比例: {comparison['param_reduction']:.4f}")
    
    # 微调
    if fine_tune_epochs > 0:
        print(f"正在微调剪枝后的模型，轮数: {fine_tune_epochs}...")
        pruned_model = fine_tune(
            pruned_model, train_loader, val_loader, device, epochs=fine_tune_epochs
        )
    
    # 评估
    print("正在评估剪枝后的模型...")
    pruned_model = pruned_model.to(device)
    metrics = evaluate_model(pruned_model, test_loader, device, classes)
    
    print(f"剪枝后模型准确率: {metrics['accuracy']:.4f}")
    
    # 保存结果
    os.makedirs(output_dir, exist_ok=True)

    
    # 保存模型
    model_path = os.path.join(output_dir, f"pruned_{pruning_method}_{int(pruning_ratio*100)}.pth")
    torch.save(pruned_model.state_dict(), model_path)
    print(f"剪枝后的模型已保存到: {model_path}")
    
    # 汇总结果
    results = {
        "pruning_method": pruning_method,
        "pruning_ratio": pruning_ratio,
        "sparsity": sparsity,
        "param_reduction": comparison["param_reduction"],
        "accuracy": metrics["accuracy"],
        "precision": metrics["precision"],
        "recall": metrics["recall"],
        "f1": metrics["f1"]
    }
    
    return pruned_model, results


def main(args):
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载数据
    train_loader, val_loader, test_loader, classes = create_dataloaders(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers
    )
    
    # 加载原始模型
    print(f"加载原始模型: {args.model_path}")
    model = create_model(num_classes=len(classes), pretrained=False)
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model = model.to(device)
    
    # 评估原始模型
    print("评估原始模型...")
    original_metrics = evaluate_model(model, test_loader, device, classes)
    print(f"原始模型准确率: {original_metrics['accuracy']:.4f}")
    
    # 剪枝参数
    pruning_methods = args.pruning_methods.split(',')
    pruning_ratios = [float(r) for r in args.pruning_ratios.split(',')]
    
    # 存储所有结果
    all_results = []
    all_models = {}
    all_models["original"] = model
    
    # 开始剪枝
    for method in pruning_methods:
        for ratio in pruning_ratios:
            pruned_model, results = prune_and_evaluate(
                model=model,
                train_loader=train_loader,
                val_loader=val_loader,
                test_loader=test_loader,
                classes=classes,
                device=device,
                pruning_method=method,
                pruning_ratio=ratio,
                fine_tune_epochs=args.fine_tune_epochs,
                output_dir=args.output_dir
            )
            
            all_results.append(results)
            model_key = f"{method}_{int(ratio*100)}"
            all_models[model_key] = pruned_model
    
    # 比较和可视化
    if len(all_results) > 0:
        # 提取对比数据
        model_names = ["original"] + [f"{r['pruning_method']}_{int(r['pruning_ratio']*100)}" for r in all_results]
        accuracies = [original_metrics["accuracy"] * 100] + [r["accuracy"] * 100 for r in all_results]
        param_counts = [count_parameters(all_models["original"]) / 1e6]
        param_counts += [count_parameters(all_models[name]) / 1e6 for name in model_names[1:]]
        
        # 获取模型文件大小
        file_sizes = []
        for name in model_names:
            if name == "original":
                path = args.model_path
            else:
                method, ratio = name.split('_')
                path = os.path.join(args.output_dir, f"pruned_{method}_{ratio}.pth")
            
            if os.path.exists(path):
                file_sizes.append(os.path.getsize(path) / (1024 * 1024))  # MB
            else:
                # 临时保存模型以获取大小
                temp_path = os.path.join(args.output_dir, f"temp_{name}.pth")
                torch.save(all_models[name].state_dict(), temp_path)
                file_sizes.append(os.path.getsize(temp_path) / (1024 * 1024))  # MB
                os.remove(temp_path)
        
        # 绘制比较图
        comparison_path = os.path.join(args.output_dir, "models_comparison.png")
        plot_model_comparison(
            model_names, accuracies, param_counts, file_sizes, save_path=comparison_path
        )
        print(f"模型比较图已保存到: {comparison_path}")
    
    print("剪枝完成！")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="模型剪枝")
    
    # 数据参数
    parser.add_argument("--data_dir", type=str, default="PatternNet/images", help="数据集目录")
    parser.add_argument("--batch_size", type=int, default=32, help="批量大小")
    
    # 模型参数
    parser.add_argument("--model_path", type=str, required=True, help="原始模型路径")
    
    # 剪枝参数
    parser.add_argument("--pruning_methods", type=str, default="global,structured,unstructured", 
                        help="剪枝方法，可以多选，用逗号分隔")
    parser.add_argument("--pruning_ratios", type=str, default="0.3,0.5", 
                        help="剪枝比例，可以多选，用逗号分隔")
    parser.add_argument("--fine_tune_epochs", type=int, default=5, 
                        help="剪枝后微调的轮数")
    
    # 硬件参数
    parser.add_argument("--device", type=str, default="cuda", help="计算设备")
    parser.add_argument("--num_workers", type=int, default=4, help="数据加载线程数")
    
    # 输出参数
    parser.add_argument("--output_dir", type=str, default="pruned_models", 
                        help="剪枝模型输出目录")
    
    args = parser.parse_args()
    
    main(args) 