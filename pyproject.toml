[build-system]
requires = ["setuptools>=68.0", "wheel", "setuptools-scm"]
build-backend = "setuptools.build_meta"

[project]
name = "remote-sensing-classification"
version = "0.1.0"
description = "Remote Sensing Image Classification System with Model Optimization"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Remote Sensing Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Remote Sensing Team", email = "<EMAIL>"}
]
keywords = [
    "remote sensing",
    "image classification", 
    "deep learning",
    "computer vision",
    "pytorch",
    "machine learning",
    "model optimization"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Image Recognition",
    "Operating System :: OS Independent",
]
requires-python = ">=3.8"
dependencies = [
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "scikit-learn>=1.3.0",
    "matplotlib>=3.7.0",
    "Pillow>=10.0.0",
    "tqdm>=4.65.0",
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.23.0",
    "python-multipart>=0.0.6",
    "gradio>=3.40.0",
    "timm>=0.9.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "rich>=13.0.0",
    "click>=8.1.0",
]

[project.optional-dependencies]
web = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.23.0",
    "python-multipart>=0.0.6",
    "jinja2>=3.1.0",
    "aiofiles>=23.0.0",
    "websockets>=11.0.0",
]
ui = [
    "gradio>=3.40.0",
    "streamlit>=1.25.0",
    "plotly>=5.15.0",
]
monitoring = [
    "wandb>=0.15.0",
    "mlflow>=2.5.0",
    "tensorboard>=2.13.0",
]
optimization = [
    "pytorch-lightning>=2.0.0",
    "torchmetrics>=1.0.0",
    "albumentations>=1.3.0",
]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.3.0",
    "sphinx>=7.1.0",
    "jupyter>=1.0.0",
]
all = [
    "remote-sensing-classification[web,ui,monitoring,optimization]"
]

[project.urls]
Homepage = "https://github.com/your-org/remote-sensing-classification"
Documentation = "https://github.com/your-org/remote-sensing-classification/docs"
Repository = "https://github.com/your-org/remote-sensing-classification"
"Bug Tracker" = "https://github.com/your-org/remote-sensing-classification/issues"

[project.scripts]
rsic-train = "src.train:main"
rsic-evaluate = "src.evaluate:main"
rsic-predict = "src.app:main"
rsic-web = "src.web_app:main"
rsic-demo = "src.demo_web_app:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.yml", "*.yaml", "*.json", "*.cfg", "*.ini"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["torch", "torchvision", "numpy", "pandas", "sklearn", "matplotlib", "PIL", "fastapi", "gradio"]

# MyPy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "torch.*",
    "torchvision.*",
    "sklearn.*",
    "matplotlib.*",
    "PIL.*",
    "gradio.*",
    "timm.*",
    "wandb.*",
    "mlflow.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "web: marks tests as web interface tests",
    "model: marks tests as model-related tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Flake8 configuration (in setup.cfg since flake8 doesn't support pyproject.toml yet)
# See setup.cfg for flake8 configuration

# Bandit configuration
[tool.bandit]
exclude_dirs = ["tests", "test_*"]
skips = ["B101", "B601"]

# Pre-commit configuration
[tool.pre-commit]
repos = [
    {
        repo = "https://github.com/pre-commit/pre-commit-hooks",
        rev = "v4.4.0",
        hooks = [
            {id = "trailing-whitespace"},
            {id = "end-of-file-fixer"},
            {id = "check-yaml"},
            {id = "check-added-large-files"},
            {id = "check-json"},
            {id = "check-toml"},
            {id = "check-xml"},
            {id = "debug-statements"},
            {id = "check-docstring-first"},
            {id = "check-merge-conflict"},
            {id = "check-executables-have-shebangs"},
        ]
    },
    {
        repo = "https://github.com/psf/black",
        rev = "23.7.0",
        hooks = [{id = "black"}]
    },
    {
        repo = "https://github.com/pycqa/isort",
        rev = "5.12.0",
        hooks = [{id = "isort"}]
    },
    {
        repo = "https://github.com/pycqa/flake8",
        rev = "6.0.0",
        hooks = [{id = "flake8"}]
    },
    {
        repo = "https://github.com/pre-commit/mirrors-mypy",
        rev = "v1.5.1",
        hooks = [{id = "mypy"}]
    },
]
