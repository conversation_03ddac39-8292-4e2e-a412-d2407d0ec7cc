import os
import copy
import torch
import torch.nn as nn
import torch.quantization
from typing import Dict, Union
from torch.utils.data import DataLoader
import torchvision.models.quantization as models_quant
from models.base_model import create_float_model


def prepare_model_for_quantization(model: nn.Module) -> nn.Module:
    """
    准备模型进行量化，添加必要的量化/反量化节点

    Args:
        model: 原始PyTorch模型

    Returns:
        准备好量化的模型副本
    """
    # 复制模型避免修改原始模型
    qmodel = copy.deepcopy(model)

    # 将模型转换为可量化模型
    qmodel.model.eval()  # 确保处于评估模式

    # 用量化感知模块替换原始模块
    qmodel = torch.quantization.fuse_modules(
        qmodel.model, [["conv1", "bn1", "relu"]], inplace=True
    )

    for name, module in qmodel.named_children():
        if isinstance(module, nn.Sequential):
            for n, m in module.named_children():
                if isinstance(m, nn.ReLU):
                    module._modules[n] = nn.ReLU(inplace=False)
        elif isinstance(module, nn.ReLU):
            qmodel._modules[name] = nn.ReLU(inplace=False)

    return qmodel


def quantize_model_static(
    calibration_loader: DataLoader,
    num_calibration_batches: int = 64,
) -> nn.Module:
    """
    对模型进行静态量化

    Args:
        model: 原始PyTorch模型
        calibration_loader: 用于校准的数据加载器
        num_calibration_batches: 用于校准的批次数量
        backend: 量化后端。'auto' 会尝试根据 CPU 架构选择,
                 或者直接指定 'fbgemm' (推荐用于 x86) 或 'qnnpack' (推荐用于 ARM)。

    Returns:
        量化后的模型
    """
    float_model_instance = create_float_model(num_classes=38, pretrained=False)
    float_model_instance.load_float_weights("outputs/run_20250410_111418/resnet50/model.pth", map_location="cpu")
    float_model_instance.eval()
    print("浮点权重加载完成。")

    print("\n创建量化就绪模型结构...")

    quantization_model = models_quant.resnet50(weights=None, quantize=False)

    in_features = quantization_model.fc.in_features
    quantization_model.fc = nn.Linear(in_features, 38)

    quantization_model.eval()

    print("将浮点权重迁移到量化模型结构...")

    float_state_dict = float_model_instance.state_dict()

    new_state_dict = {}
    prefix = "model."
    keys_corrected = 0
    keys_skipped = 0
    print(f"原始 state_dict 包含 {len(float_state_dict)} 个键。")
    for k, v in float_state_dict.items():
        if k.startswith(prefix):
            # Remove the prefix "model."
            new_key = k[len(prefix) :]
            new_state_dict[new_key] = v
            keys_corrected += 1
        else:
            # Keep track of keys that didn't have the prefix, if any
            keys_skipped += 1
            print(f"  跳过键: {k} (没有 '{prefix}' 前缀)")
            # new_state_dict[k] = v # Uncomment if you need to keep non-prefixed keys
    print(f"键名修正: {keys_corrected} 个键移除了 '{prefix}' 前缀。")
    if keys_skipped > 0:
        print(f"键名警告: {keys_skipped} 个键没有 '{prefix}' 前缀，已被跳过。")

    # --- Load the corrected state dict ---
    if not new_state_dict:
        print(
            "错误: 修正后的 state_dict 为空！请检查模型定义和原始 state_dict 的键名。"
        )
        exit()

    print(f"尝试加载包含 {len(new_state_dict)} 个键的修正后 state_dict...")
    try:
        quantization_model.load_state_dict(new_state_dict, strict=True)
        print("权重迁移成功 (strict=True)。")
    except RuntimeError as e:
        print(f"使用 strict=True 加载权重失败: {e}")
        print("这通常是因为 'num_batches_tracked' 等非参数键不匹配。")
        print("尝试使用 strict=False...")
        try:
            quantization_model.load_state_dict(new_state_dict, strict=False)
            print("权重迁移成功 (strict=False)。")
        except Exception as e2:
            print(f"使用 strict=False 加载权重也失败: {e2}")
            # Print some keys for comparison
            print("\n目标模型期望的前 10 个键:")
            for i, key in enumerate(quantization_model.state_dict().keys()):
                if i >= 10:
                    break
                print(f"  - {key}")
            print("\n我们尝试加载的前 10 个键:")
            for i, key in enumerate(new_state_dict.keys()):
                if i >= 10:
                    break
                print(f"  - {key}")
            print("\n请仔细检查模型结构和 state_dict 键。")
            exit()

    quantization_model.eval()

    print("\n准备静态量化...")
    backend = "fbgemm"  # or "qnnpack"
    # Check backend availability
    if backend not in torch.backends.quantized.supported_engines:
        print(f"警告: 后端 {backend} 不可用, 尝试切换...")
        backend = "qnnpack" if backend == "fbgemm" else "fbgemm"
        if backend not in torch.backends.quantized.supported_engines:
            print("错误: 可用的量化后端均不支持!")
            exit()

    quantization_model.qconfig = torch.quantization.get_default_qconfig(backend)
    print(f"使用量化配置: {quantization_model.qconfig}")

    if hasattr(quantization_model, "fuse_model") and callable(
        quantization_model.fuse_model
    ):
        print("执行模型内置的 fuse_model()...")
        quantization_model.fuse_model()  # Fuse after loading weights
    else:
        print("警告: 模型没有内置 fuse_model() 方法或该方法不可调用，跳过融合。")

    # Prepare the model by inserting observers
    print("准备模型 (插入观察者)...")
    torch.quantization.prepare(quantization_model, inplace=True)

    print(f"\n--- 开始数据校准 (使用 {num_calibration_batches} 个批次) ---")

    device = torch.device("cpu")
    quantization_model.to(device)

    with torch.no_grad():
        # 迭代校准数据加载器
        for i, (calibration_data, _) in enumerate(
            calibration_loader
        ):  # 假设加载器返回 (数据, 标签)
            if i >= num_calibration_batches:
                print(f"\n达到指定的 {num_calibration_batches} 个校准批次，停止校准。")
                break  # 达到指定的批次数后停止

            # 将校准数据移动到目标设备
            calibration_data = calibration_data.to(device)

            # 通过模型运行校准数据
            # 这个前向传播过程会让插入的观察者记录激活值的统计信息
            quantization_model(calibration_data)

            # 打印进度
            print(f"\r  校准进度: 批次 {i + 1}/{num_calibration_batches}", end="")

    print("\n--- 数据校准完成 ---")

    print("\n转换模型为量化模型...")
    torch.quantization.convert(quantization_model, inplace=True)
    print("模型转换完成。")

    quantization_model.eval()
    scripted_quantized_model = torch.jit.script(quantization_model)
    scripted_quantized_model.save("outputs/run_20250410_111418/resnet50/quantized/quantized.pt")
    print("JIT 量化模型保存成功。")

    return quantization_model


def get_size_of_model(model: nn.Module, file_path: str = "temp_model.pt") -> int:
    """
    获取模型的大小（单位：字节）

    Args:
        model: PyTorch模型
        file_path: 临时保存模型的路径

    Returns:
        模型大小（字节）
    """
    torch.save(model.state_dict(), file_path)
    size = os.path.getsize(file_path)
    os.remove(file_path)  # 删除临时文件
    return size


def compare_models_size(
    original_model: nn.Module, quantized_model: nn.Module
) -> Dict[str, Union[int, float]]:
    """
    比较原始模型和量化后模型的大小

    Args:
        original_model: 原始模型
        quantized_model: 量化后的模型

    Returns:
        包含比较结果的字典
    """
    original_size = get_size_of_model(original_model, "temp_original.pt")
    quantized_size = get_size_of_model(quantized_model, "temp_quantized.pt")

    size_reduction = 1.0 - (quantized_size / original_size)

    return {
        "original_size_bytes": original_size,
        "quantized_size_bytes": quantized_size,
        "size_reduction_ratio": size_reduction,
        "size_reduction_percentage": size_reduction * 100,
    }
