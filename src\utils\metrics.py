import os
import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score, precision_recall_fscore_support
from sklearn.metrics import precision_score, recall_score, f1_score
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns


def compute_accuracy(outputs: torch.Tensor, targets: torch.Tensor) -> float:
    """
    计算分类准确率
    
    Args:
        outputs: 模型输出的预测值
        targets: 真实标签
        
    Returns:
        准确率，取值范围[0, 1]
    """
    _, predicted = torch.max(outputs, 1)
    correct = (predicted == targets).sum().item()
    total = targets.size(0)
    return correct / total


def compute_metrics(
    all_predictions: np.ndarray, 
    all_targets: np.ndarray, 
    classes: List[str] = None
) -> Dict[str, any]:
    """
    计算多种评估指标
    
    Args:
        all_predictions: 所有样本的预测类别索引
        all_targets: 所有样本的真实类别索引
        classes: 类别名称列表
        
    Returns:
        包含各种评估指标的字典
    """
    # 计算准确率
    accuracy = accuracy_score(all_targets, all_predictions)
    
    # 计算精确率、召回率和F1分数
    precision, recall, f1, support = precision_recall_fscore_support(
        all_targets, all_predictions, average='macro'
    )
    
    # 计算每个类别的精确率、召回率和F1分数
    class_precision, class_recall, class_f1, class_support = precision_recall_fscore_support(
        all_targets, all_predictions, average=None
    )
    
    # 创建混淆矩阵
    cm = confusion_matrix(all_targets, all_predictions)
    
    # 获取每个类别的指标
    class_metrics = {}
    if classes is not None:
        for i, class_name in enumerate(classes):
            class_metrics[class_name] = {
                'precision': class_precision[i],
                'recall': class_recall[i],
                'f1': class_f1[i],
                'support': class_support[i]
            }
    
    # 汇总指标
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'confusion_matrix': cm,
        'class_metrics': class_metrics
    }
    
    return metrics


def get_classification_report(
    all_predictions: np.ndarray, 
    all_targets: np.ndarray, 
    classes: List[str] = None
) -> str:
    """
    生成分类报告
    
    Args:
        all_predictions: 所有样本的预测类别索引
        all_targets: 所有样本的真实类别索引
        classes: 类别名称列表
        
    Returns:
        格式化的分类报告字符串
    """
    target_names = classes if classes is not None else None
    return classification_report(all_targets, all_predictions, target_names=target_names)


def evaluate_model(
    model, 
    dataloader, 
    device, 
    classes=None, 
    return_predictions=False
):
    """
    评估模型性能
    
    Args:
        model: 模型
        dataloader: 数据加载器
        device: 计算设备
        classes: 类别列表
        return_predictions: 是否返回所有预测结果
        
    Returns:
        包含各种评估指标的字典
    """
    model.eval()
    
    all_preds = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for inputs, labels in tqdm(dataloader, desc="Evaluating"):
            inputs, labels = inputs.to(device), labels.to(device)
            
            # 前向传播
            outputs = model(inputs)
            probs = torch.nn.functional.softmax(outputs, dim=1)
            _, preds = torch.max(outputs, 1)
            
            # 收集预测和标签
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(probs.cpu().numpy())
    
    # 转换为NumPy数组
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)
    all_probs = np.array(all_probs)
    
    # 计算各种指标
    metrics = {}
    
    # 计算基本指标
    correct = (all_preds == all_labels).sum()
    total = len(all_labels)
    accuracy = correct / total if total > 0 else 0
    metrics['accuracy'] = accuracy
    
    try:
        # 如果提供了类别列表，计算更详细的指标
        if classes is not None:
            # 计算混淆矩阵
            try:
                cm = confusion_matrix(all_labels, all_preds)
                metrics['confusion_matrix'] = cm
            except Exception as e:
                print(f"计算混淆矩阵时出错: {e}")
                metrics['confusion_matrix'] = None
            
            # 计算精确率、召回率、F1分数
            try:
                precision = precision_score(all_labels, all_preds, average='macro', zero_division=0)
                recall = recall_score(all_labels, all_preds, average='macro', zero_division=0)
                f1 = f1_score(all_labels, all_preds, average='macro', zero_division=0)
                
                metrics['precision'] = precision
                metrics['recall'] = recall
                metrics['f1'] = f1
            except Exception as e:
                print(f"计算精确率/召回率/F1分数时出错: {e}")
                metrics['precision'] = 0
                metrics['recall'] = 0
                metrics['f1'] = 0
            
            # 计算每个类别的准确率
            try:
                class_correct = {}
                class_total = {}
                class_accuracy = {}
                
                for pred, label in zip(all_preds, all_labels):
                    if label not in class_total:
                        class_total[label] = 0
                        class_correct[label] = 0
                    
                    class_total[label] += 1
                    if pred == label:
                        class_correct[label] += 1
                
                for i, class_name in enumerate(classes):
                    if i in class_total and class_total[i] > 0:
                        class_accuracy[class_name] = class_correct[i] / class_total[i]
                    else:
                        class_accuracy[class_name] = 0
                
                metrics['class_accuracy'] = class_accuracy
            except Exception as e:
                print(f"计算每个类别准确率时出错: {e}")
                metrics['class_accuracy'] = {}
    except Exception as e:
        print(f"计算详细指标时出错: {e}")
    
    # 如果需要，返回所有预测结果
    if return_predictions:
        metrics['predictions'] = all_preds
        metrics['labels'] = all_labels
        metrics['probabilities'] = all_probs
    
    return metrics


def count_parameters(model: nn.Module) -> Tuple[int, int]:
    """
    计算模型的总参数量和非零参数量。

    Args:
        model: PyTorch 模型。

    Returns:
        一个元组 (total_params, nonzero_params)，分别包含总参数量和非零参数量。
    """
    total_params = 0
    nonzero_params = 0
    # 遍历所有参数，包括 requires_grad=False 的参数 (例如 buffers 或者冻结的层)
    # 确保计算所有参数，无论是否需要梯度
    for param in model.parameters():
         param_numel = param.numel()
         total_params += param_numel
         nonzero_params += torch.count_nonzero(param.detach()).item()

    # 注意: buffer 通常不被视为 model.parameters() 的一部分，如果需要计算 buffer，
    # 需要单独遍历 model.buffers()
    # 但对于比较模型大小，通常只关注 parameters

    return total_params, nonzero_params


def calculate_sparsity(model: nn.Module) -> float:
    """计算模型的整体稀疏度（参数值为零的比例）"""
    total_params, nonzero_params = count_parameters(model)
    if total_params == 0:
        return 0.0
    # 确保使用浮点数进行除法
    sparsity = (1.0 - float(nonzero_params) / float(total_params)) * 100.0
    return sparsity


def calculate_metrics(all_labels: List[int], all_preds: List[int], classes: List[str], average: str = 'weighted') -> Dict[str, float]:
    """
    计算分类指标

    Args:
        all_labels: 真实标签列表
        all_preds: 预测标签列表
        classes: 类别名称列表
        average: 计算多类别指标的平均方法 ('micro', 'macro', 'weighted', None)

    Returns:
        包含指标的字典 (accuracy, precision, recall, f1)
    """
    accuracy = accuracy_score(all_labels, all_preds)
    precision, recall, f1, _ = precision_recall_fscore_support(all_labels, all_preds, average=average, zero_division=0)

    report = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1
    }
    
    print(f"\nClassification Report (average='{average}'):")
    # 打印简化的报告
    print(f"  Accuracy:  {accuracy:.4f}")
    print(f"  Precision: {precision:.4f}")
    print(f"  Recall:    {recall:.4f}")
    print(f"  F1 Score:  {f1:.4f}")

    # 打印详细的分类报告 (每个类别)
    try:
        detailed_report = classification_report(all_labels, all_preds, target_names=classes, zero_division=0)
        print("\nDetailed Classification Report:")
        print(detailed_report)
    except ValueError as e:
        print(f"\n无法生成详细报告: {e}")
        print("确保提供的 'classes' 列表与标签中的类别数量和顺序匹配。")
    
    return report


def plot_confusion_matrix(cm: np.ndarray, classes: List[str], save_path: str, title: str = 'Confusion Matrix'):
    """
    绘制并保存混淆矩阵图

    Args:
        cm: 混淆矩阵 (numpy数组)
        classes: 类别名称列表
        save_path: 保存图像的路径
        title: 图像标题
    """
    plt.figure(figsize=(15, 12))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=classes, yticklabels=classes)
    plt.title(title)
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path)
    plt.close() # 关闭图像，释放内存
    print(f"混淆矩阵已保存到: {save_path}")

# 其他函数保持不变... 