"""
数据集管理业务逻辑服务

复用app.py中的数据集功能，提供API服务层
"""

import os
import sys
import base64
from typing import List, Tuple, Optional, Dict, Any
from PIL import Image

# 添加父目录到路径以导入app.py中的函数
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入app.py中的数据集功能
from app import (
    get_dataset_sample_images, get_dataset_comparison_images
)

from ..config import settings
from ..models.dataset import ImageInfo, DatasetClassInfo, DatasetInfo

class DatasetService:
    """数据集管理服务类"""
    
    def __init__(self):
        """初始化数据集服务"""
        pass
    
    def _encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """将图片编码为Base64字符串"""
        try:
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                return f"data:image/jpeg;base64,{encoded_string}"
        except Exception as e:
            print(f"编码图片失败 {image_path}: {e}")
            return None
    
    def _get_file_size(self, file_path: str) -> Optional[int]:
        """获取文件大小"""
        try:
            return os.path.getsize(file_path)
        except Exception:
            return None
    
    def _create_image_info(self, image_path: str, class_info: str, include_base64: bool = False) -> ImageInfo:
        """创建图片信息对象"""
        filename = os.path.basename(image_path)
        class_name = class_info.split(":")[0].strip()
        
        image_info = ImageInfo(
            path=image_path,
            filename=filename,
            class_name=class_name,
            size_bytes=self._get_file_size(image_path)
        )
        
        if include_base64:
            image_info.base64_data = self._encode_image_to_base64(image_path)
        
        return image_info
    
    def get_dataset_samples(self, dataset_type: str = "old", num_classes: int = 3, 
                          num_images_per_class: int = 3, include_base64: bool = False) -> Tuple[List[ImageInfo], List[str], str]:
        """
        获取数据集示例图片
        
        Args:
            dataset_type: 数据集类型 ("old" 或 "new")
            num_classes: 选择的类别数量
            num_images_per_class: 每个类别选择的图片数量
            include_base64: 是否包含Base64编码
            
        Returns:
            (图片信息列表, 类别列表, 消息)
        """
        try:
            # 确定数据集路径
            if dataset_type == "old":
                dataset_path = str(settings.OLD_DATASET_DIR)
            elif dataset_type == "new":
                dataset_path = str(settings.NEW_DATASET_DIR)
            else:
                return [], [], f"不支持的数据集类型: {dataset_type}"
            
            # 调用app.py中的函数
            image_paths, class_infos = get_dataset_sample_images(
                dataset_path, num_classes, num_images_per_class
            )
            
            if not image_paths:
                return [], [], f"{dataset_type}数据集为空或不存在"
            
            # 转换为ImageInfo对象
            image_infos = []
            classes_found = set()
            
            for img_path, class_info in zip(image_paths, class_infos):
                image_info = self._create_image_info(img_path, class_info, include_base64)
                image_infos.append(image_info)
                classes_found.add(image_info.class_name)
            
            message = f"成功获取{dataset_type}数据集的{len(image_infos)}张示例图片，涵盖{len(classes_found)}个类别"
            return image_infos, list(classes_found), message
            
        except Exception as e:
            return [], [], f"获取数据集示例失败: {str(e)}"
    
    def get_dataset_comparison(self, num_images_per_dataset: int = 4, include_base64: bool = False, 
                             specific_class: Optional[str] = None) -> Tuple[List[ImageInfo], List[ImageInfo], Optional[str], str]:
        """
        获取新旧数据集对比图片
        
        Args:
            num_images_per_dataset: 每个数据集选择的图片数量
            include_base64: 是否包含Base64编码
            specific_class: 指定对比的类别名称
            
        Returns:
            (旧数据集图片, 新数据集图片, 对比类别, 消息)
        """
        try:
            # 调用app.py中的函数
            old_image_paths, old_class_infos, new_image_paths, new_class_infos = get_dataset_comparison_images()
            
            if not old_image_paths and not new_image_paths:
                return [], [], None, "新旧数据集都为空或不存在"
            
            # 转换为ImageInfo对象
            old_image_infos = []
            new_image_infos = []
            comparison_class = None
            
            # 处理旧数据集图片
            for img_path, class_info in zip(old_image_paths, old_class_infos):
                image_info = self._create_image_info(img_path, class_info, include_base64)
                old_image_infos.append(image_info)
                if comparison_class is None:
                    comparison_class = image_info.class_name
            
            # 处理新数据集图片
            for img_path, class_info in zip(new_image_paths, new_class_infos):
                image_info = self._create_image_info(img_path, class_info, include_base64)
                new_image_infos.append(image_info)
            
            message = f"成功获取数据集对比图片，旧数据集{len(old_image_infos)}张，新数据集{len(new_image_infos)}张"
            if comparison_class:
                message += f"，对比类别: {comparison_class}"
            
            return old_image_infos, new_image_infos, comparison_class, message
            
        except Exception as e:
            return [], [], None, f"获取数据集对比失败: {str(e)}"
    
    def get_dataset_info(self, dataset_type: str) -> Tuple[Optional[DatasetInfo], str]:
        """
        获取数据集详细信息
        
        Args:
            dataset_type: 数据集类型 ("old" 或 "new")
            
        Returns:
            (数据集信息, 消息)
        """
        try:
            # 确定数据集路径
            if dataset_type == "old":
                dataset_path = str(settings.OLD_DATASET_DIR)
            elif dataset_type == "new":
                dataset_path = str(settings.NEW_DATASET_DIR)
            else:
                return None, f"不支持的数据集类型: {dataset_type}"
            
            # 检查数据集是否存在
            if not os.path.exists(dataset_path):
                return DatasetInfo(
                    dataset_path=dataset_path,
                    exists=False,
                    total_classes=0,
                    total_images=0,
                    classes=[]
                ), f"{dataset_type}数据集目录不存在"
            
            # 获取所有类别目录
            class_dirs = [d for d in os.listdir(dataset_path) 
                         if os.path.isdir(os.path.join(dataset_path, d))]
            
            classes_info = []
            total_images = 0
            
            for class_name in class_dirs:
                class_path = os.path.join(dataset_path, class_name)
                
                # 获取该类别下的所有图片
                image_files = [f for f in os.listdir(class_path) 
                             if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                
                # 获取示例图片路径（最多3张）
                sample_images = []
                for img_file in image_files[:3]:
                    sample_images.append(os.path.join(class_path, img_file))
                
                class_info = DatasetClassInfo(
                    class_name=class_name,
                    image_count=len(image_files),
                    sample_images=sample_images
                )
                classes_info.append(class_info)
                total_images += len(image_files)
            
            dataset_info = DatasetInfo(
                dataset_path=dataset_path,
                exists=True,
                total_classes=len(class_dirs),
                total_images=total_images,
                classes=classes_info
            )
            
            message = f"成功获取{dataset_type}数据集信息，共{len(class_dirs)}个类别，{total_images}张图片"
            return dataset_info, message
            
        except Exception as e:
            return None, f"获取数据集信息失败: {str(e)}"
    
    def get_dataset_stats(self) -> Tuple[Dict[str, Any], Dict[str, Any], str]:
        """
        获取新旧数据集统计信息
        
        Returns:
            (旧数据集统计, 新数据集统计, 消息)
        """
        try:
            old_info, old_msg = self.get_dataset_info("old")
            new_info, new_msg = self.get_dataset_info("new")
            
            old_stats = {
                "exists": old_info.exists if old_info else False,
                "total_classes": old_info.total_classes if old_info else 0,
                "total_images": old_info.total_images if old_info else 0,
                "path": old_info.dataset_path if old_info else str(settings.OLD_DATASET_DIR)
            }
            
            new_stats = {
                "exists": new_info.exists if new_info else False,
                "total_classes": new_info.total_classes if new_info else 0,
                "total_images": new_info.total_images if new_info else 0,
                "path": new_info.dataset_path if new_info else str(settings.NEW_DATASET_DIR)
            }
            
            message = f"成功获取数据集统计信息"
            return old_stats, new_stats, message
            
        except Exception as e:
            return {}, {}, f"获取数据集统计失败: {str(e)}"

# 全局数据集服务实例
dataset_service = DatasetService()
