# =============================================================================
# Docker Compose for Remote Sensing Image Classification System
# Provides both development and production deployment options
# =============================================================================

version: '3.8'

services:
  # =============================================================================
  # Production Service
  # =============================================================================
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        PYTHON_VERSION: 3.10
        CUDA_VERSION: 12.2.2
        UBUNTU_VERSION: 22.04
        APP_USER: appuser
        APP_UID: 1000
        APP_GID: 1000
    image: remote-sensing-classification:latest
    container_name: rsic-app
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      - WORKERS=4
      - TIMEOUT=120
    volumes:
      # Mount data directories
      - ./outputs:/app/outputs
      - ./old_dataset:/app/old_dataset:ro
      - ./new_dataset:/app/new_dataset:ro
      - ./logs:/app/logs
      # Mount configuration
      - ./.env:/app/.env:ro
    healthcheck:
      test: ["CMD", "/app/healthcheck.sh"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G
    networks:
      - rsic-network

  # =============================================================================
  # Development Service
  # =============================================================================
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
      args:
        PYTHON_VERSION: 3.10
        CUDA_VERSION: 12.2.2
        UBUNTU_VERSION: 22.04
        APP_USER: appuser
        APP_UID: 1000
        APP_GID: 1000
    image: remote-sensing-classification:dev
    container_name: rsic-app-dev
    ports:
      - "8000:8000"  # Web app
      - "8888:8888"  # Jupyter
      - "6006:6006"  # TensorBoard
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - RELOAD=true
    volumes:
      # Mount source code for hot reload
      - ./src:/app/src
      - ./static:/app/static
      - ./outputs:/app/outputs
      - ./old_dataset:/app/old_dataset:ro
      - ./new_dataset:/app/new_dataset:ro
      - ./logs:/app/logs
      - ./imgs:/app/imgs
      # Mount configuration
      - ./.env:/app/.env:ro
    networks:
      - rsic-network
    profiles:
      - dev

  # =============================================================================
  # GPU-enabled Production Service (for CUDA support)
  # =============================================================================
  app-gpu:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        PYTHON_VERSION: 3.10
        CUDA_VERSION: 12.2.2
        UBUNTU_VERSION: 22.04
        APP_USER: appuser
        APP_UID: 1000
        APP_GID: 1000
    image: remote-sensing-classification:gpu
    container_name: rsic-app-gpu
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      - WORKERS=2  # Fewer workers for GPU
      - TIMEOUT=300  # Longer timeout for GPU inference
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ./outputs:/app/outputs
      - ./old_dataset:/app/old_dataset:ro
      - ./new_dataset:/app/new_dataset:ro
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 16G
        reservations:
          memory: 8G
    healthcheck:
      test: ["CMD", "/app/healthcheck.sh"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s  # Longer start period for GPU initialization
    networks:
      - rsic-network
    profiles:
      - gpu

  # =============================================================================
  # Monitoring Service (Optional)
  # =============================================================================
  monitoring:
    image: prom/prometheus:latest
    container_name: rsic-monitoring
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - rsic-network
    profiles:
      - monitoring

# =============================================================================
# Networks
# =============================================================================
networks:
  rsic-network:
    driver: bridge
    name: rsic-network

# =============================================================================
# Volumes (for persistent data)
# =============================================================================
volumes:
  rsic-data:
    name: rsic-data
  rsic-logs:
    name: rsic-logs
