import os
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
import itertools
import matplotlib as mpl

import torch
from torch.utils.tensorboard import SummaryWriter
from sklearn.metrics import confusion_matrix

# 设置中文字体支持
def set_chinese_font():
    """
    设置matplotlib中文字体支持
    """
    try:
        # 尝试设置中文字体
        plt.rcParams["font.sans-serif"] = [
            "Noto Sans CJK SC",
            "Noto Sans CJK TC",
            "Noto Sans CJK JP",
            "Noto Sans CJK KR",
        ]  # 简体中文
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        
    except Exception as e:
        print(f"设置中文字体时出错: {e}")
        print("将使用默认字体，中文可能显示为方块")

# 调用函数设置字体
set_chinese_font()

def plot_training_curves(
    train_losses: List[float], 
    val_losses: List[float], 
    train_accs: List[float], 
    val_accs: List[float],
    save_path: str = 'training_curves.png'
):
    """
    绘制训练和验证的损失曲线与准确率曲线
    
    Args:
        train_losses: 训练损失列表
        val_losses: 验证损失列表
        train_accs: 训练准确率列表
        val_accs: 验证准确率列表
        save_path: 图像保存路径
    """
    epochs = range(1, len(train_losses) + 1)
    
    plt.figure(figsize=(12, 5))
    
    # 绘制损失曲线
    plt.subplot(1, 2, 1)
    plt.plot(epochs, train_losses, 'b-', label='训练损失')
    plt.plot(epochs, val_losses, 'r-', label='验证损失')
    plt.title('训练和验证损失')
    plt.xlabel('Epochs')
    plt.ylabel('损失')
    plt.legend()
    
    # 绘制准确率曲线
    plt.subplot(1, 2, 2)
    plt.plot(epochs, train_accs, 'b-', label='训练准确率')
    plt.plot(epochs, val_accs, 'r-', label='验证准确率')
    plt.title('训练和验证准确率')
    plt.xlabel('Epochs')
    plt.ylabel('准确率')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()


def plot_confusion_matrix(
    cm: np.ndarray,
    classes: List[str],
    normalize: bool = False,
    title: str = 'Confusion Matrix',
    cmap: plt.cm = plt.cm.Blues,
    save_path: str = 'confusion_matrix.png'
):
    """
    绘制混淆矩阵
    
    Args:
        cm: 混淆矩阵
        classes: 类别名称列表
        normalize: 是否将值归一化到[0, 1]
        title: 图表标题
        cmap: 颜色映射
        save_path: 图像保存路径
    """
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        print("显示归一化的混淆矩阵")
    else:
        print('显示混淆矩阵，无归一化')
    
    plt.figure(figsize=(10, 8))
    plt.imshow(cm, interpolation='nearest', cmap=cmap)
    plt.title(title)
    plt.colorbar()
    
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=90)
    plt.yticks(tick_marks, classes)
    
    fmt = '.2f' if normalize else 'd'
    thresh = cm.max() / 2.
    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
        plt.text(j, i, format(cm[i, j], fmt),
                 horizontalalignment="center",
                 color="white" if cm[i, j] > thresh else "black")
    
    plt.tight_layout()
    plt.ylabel('真实类别')
    plt.xlabel('预测类别')
    plt.savefig(save_path)
    plt.close()


def create_tensorboard_writer(log_dir: str) -> SummaryWriter:
    """
    创建TensorBoard摘要写入器
    
    Args:
        log_dir: 日志目录
        
    Returns:
        TensorBoard摘要写入器
    """
    os.makedirs(log_dir, exist_ok=True)
    return SummaryWriter(log_dir=log_dir)


def log_metrics_to_tensorboard(
    writer: SummaryWriter, 
    metrics: Dict[str, any], 
    step: int, 
    prefix: str = ''
):
    """
    将评估指标记录到TensorBoard
    
    Args:
        writer: TensorBoard摘要写入器
        metrics: 包含评估指标的字典
        step: 当前步骤或epoch
        prefix: 指标名称前缀，用于区分训练集和验证集等
    """
    # 记录标量指标
    for key, value in metrics.items():
        if isinstance(value, (int, float)):
            writer.add_scalar(f'{prefix}{key}', value, step)


def plot_model_comparison(
    model_names: List[str],
    accuracies: List[float],
    params_counts: List[int],
    file_sizes: List[float],
    save_path: str = 'model_comparison.png'
):
    """
    绘制模型比较图表
    
    Args:
        model_names: 模型名称列表
        accuracies: 准确率列表
        params_counts: 参数数量列表（以百万为单位）
        file_sizes: 文件大小列表（以MB为单位）
        save_path: 保存路径
    """
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 5))
    
    # 准确率比较
    ax1.bar(model_names, accuracies, color='skyblue')
    ax1.set_xlabel('模型')
    ax1.set_ylabel('准确率 (%)')
    ax1.set_title('模型准确率比较')
    
    # 参数数量比较
    ax2.bar(model_names, params_counts, color='lightgreen')
    ax2.set_xlabel('模型')
    ax2.set_ylabel('参数数量 (百万)')
    ax2.set_title('模型参数数量比较')
    
    # 文件大小比较
    ax3.bar(model_names, file_sizes, color='salmon')
    ax3.set_xlabel('模型')
    ax3.set_ylabel('文件大小 (MB)')
    ax3.set_title('模型文件大小比较')
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()


def visualize_predictions(
    images: torch.Tensor,
    true_labels: List[str],
    pred_labels: List[str],
    class_names: List[str],
    save_path: str = 'predictions.png'
):
    """
    可视化模型预测结果
    
    Args:
        images: 图像张量，形状为(N, C, H, W)
        true_labels: 真实标签索引
        pred_labels: 预测标签索引
        class_names: 类别名称列表
        save_path: 保存路径
    """
    n_images = len(images)
    n_cols = min(5, n_images)
    n_rows = (n_images + n_cols - 1) // n_cols
    
    plt.figure(figsize=(n_cols * 3, n_rows * 3))
    
    for i, (img, true_idx, pred_idx) in enumerate(zip(images, true_labels, pred_labels)):
        plt.subplot(n_rows, n_cols, i + 1)
        
        # 转换图像为numpy数组显示
        img = img.permute(1, 2, 0).cpu().numpy()
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        img = std * img + mean
        img = np.clip(img, 0, 1)
        
        plt.imshow(img)
        
        title_color = 'green' if true_idx == pred_idx else 'red'
        plt.title(f'真实: {class_names[true_idx]}\n预测: {class_names[pred_idx]}', 
                  color=title_color)
        
        plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()


def save_sample_predictions(
    inputs: torch.Tensor,
    preds: torch.Tensor,
    labels: torch.Tensor,
    classes: List[str],
    save_path: str,
    num_samples: int = 8
) -> None:
    """
    保存样本预测结果的可视化图像
    
    Args:
        inputs: 输入图像张量
        preds: 预测标签
        labels: 真实标签
        classes: 类别名称列表
        save_path: 保存路径
        num_samples: 要显示的样本数
    """
    # 确保处理的样本数不超过输入张量的大小
    num_samples = min(num_samples, inputs.size(0))
    
    # 准备图像数据
    images = inputs[:num_samples].cpu()
    predictions = preds[:num_samples].cpu()
    ground_truth = labels[:num_samples].cpu()
    
    # 设置图像布局
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    axes = axes.flatten()
    
    # 反归一化处理图像
    mean = torch.tensor([0.485, 0.456, 0.406]).reshape(1, 3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).reshape(1, 3, 1, 1)
    
    images = images * std + mean
    images = images.permute(0, 2, 3, 1).numpy()  # 从BCHW转换为BHWC
    
    # 显示每个样本
    for i in range(num_samples):
        # 获取图像和标签
        img = np.clip(images[i], 0, 1)
        pred_label = classes[predictions[i].item()]
        true_label = classes[ground_truth[i].item()]
        
        # 设置标题颜色：正确为绿色，错误为红色
        color = 'green' if predictions[i] == ground_truth[i] else 'red'
        
        # 显示图像
        axes[i].imshow(img)
        axes[i].set_title(f"预测: {pred_label}\n真实: {true_label}", color=color)
        axes[i].axis('off')
    
    # 移除多余的子图
    for i in range(num_samples, len(axes)):
        fig.delaxes(axes[i])
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close() 