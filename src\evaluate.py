import os
import argparse
import time
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import datetime
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

from data.dataset import create_dataloaders
from models.base_model import create_model
from utils.visualization import plot_confusion_matrix, save_sample_predictions


def evaluate_model(
    model: nn.Module, 
    dataloader: DataLoader, 
    device: torch.device, 
    classes: List[str],
    save_dir: Optional[str] = None,
    model_name: Optional[str] = None,
) -> Dict[str, float]:
    """
    评估模型性能
    
    Args:
        model: 要评估的模型
        dataloader: 测试数据加载器
        device: 计算设备
        classes: 类别列表
        save_dir: 结果保存目录
        model_name: 模型名称
    Returns:
        包含评估指标的字典
    """
    model.eval()
    model.to(device)
    
    # 初始化变量
    all_preds = []
    all_labels = []
    total_time = 0
    total_samples = 0
    
    # 在测试集上评估
    with torch.no_grad():
        for inputs, labels in tqdm(dataloader, desc="Evaluating"):
            inputs, labels = inputs.to(device), labels.to(device)
            batch_size = inputs.size(0)
            
            # 记录推理时间
            start_time = time.time()
            outputs = model(inputs)
            inference_time = time.time() - start_time
            total_time += inference_time
            total_samples += batch_size
            
            # 获取预测
            _, preds = torch.max(outputs, 1)
            
            # 收集预测和标签
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # 计算评估指标
    accuracy = accuracy_score(all_labels, all_preds)
    precision = precision_score(all_labels, all_preds, average='macro', zero_division=0)
    recall = recall_score(all_labels, all_preds, average='macro', zero_division=0)
    f1 = f1_score(all_labels, all_preds, average='macro', zero_division=0)
    
    # 计算平均推理时间和FPS
    avg_inference_time_ms = (total_time / total_samples) * 1000
    fps = 1000 / avg_inference_time_ms if avg_inference_time_ms > 0 else 0
    
    # 计算模型参数数量
    parameter_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    # 创建结果字典
    results = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'inference_time_ms': avg_inference_time_ms,
        'fps': fps,
        'parameter_count': parameter_count
    }
    
    # 如果提供了保存目录，保存评估结果
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存主要评估指标
        eval_results_path = os.path.join(save_dir, 'evaluation_results.txt')
        with open(eval_results_path, 'w') as f:
            for key, value in results.items():
                f.write(f"{key}: {value}\n")
        
        # 可选: 生成并保存混淆矩阵
        cm = confusion_matrix(all_labels, all_preds)
        cm_path = os.path.join(save_dir, 'confusion_matrix.png')
        plot_confusion_matrix(cm, classes, cm_path)
        
        # 可选: 保存样本预测结果
        if model_name:
            # 获取一批数据用于可视化
            inputs, labels = next(iter(dataloader))
            inputs, labels = inputs.to(device), labels.to(device)
            
            # 进行预测
            with torch.no_grad():
                outputs = model(inputs)
            
            # 获取预测类别
            _, preds = torch.max(outputs, 1)
            
            # 保存样本预测结果
            sample_path = os.path.join(save_dir, 'sample_predictions.png')
            save_sample_predictions(inputs, preds, labels, classes, sample_path)
        
        print(f"评估结果已保存到: {save_dir}")
    
    # 打印结果
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    print(f"F1 Score: {f1:.4f}")
    print(f"Avg Inference Time: {avg_inference_time_ms:.2f} ms")
    print(f"FPS: {fps:.2f}")
    print(f"Parameter Count: {parameter_count:,}")
    
    return results


def main(args):
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = args.output_dir
    if not args.no_timestamp:
        output_dir = os.path.join(output_dir, f"run_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存参数
    args_path = os.path.join(output_dir, "args.txt")
    with open(args_path, "w") as f:
        for arg, value in vars(args).items():
            f.write(f"{arg}: {value}\n")
    
    # 创建数据加载器
    _, _, test_loader, classes = create_dataloaders(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers
    )
    
    # 加载模型
    print(f"创建模型: {args.model_name}...")
    model = create_model(model_name=args.model_name, num_classes=len(classes), pretrained=False)
    print(f"加载模型权重: {args.model_path}...")
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model = model.to(device)
    
    # 评估模型
    results = evaluate_model(
        model=model,
        dataloader=test_loader,
        device=device,
        classes=classes,
        save_dir=output_dir,
        model_name=args.model_name
    )
    
    print(f"评估完成。结果已保存到 {output_dir}")
    return results


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="评估模型")
    
    # 数据参数
    parser.add_argument("--data_dir", type=str, default="PatternNet/images", help="数据集目录")
    parser.add_argument("--batch_size", type=int, default=32, help="批量大小")
    
    # 模型参数
    parser.add_argument("--model_name", type=str, default="resnet50", help="模型名称 (e.g., resnet50, densenet201, vit_s_16, swin_t)")
    parser.add_argument("--model_path", type=str, required=True, help="模型路径")
    
    # 硬件参数
    parser.add_argument("--device", type=str, default="cuda", help="设备")
    parser.add_argument("--num_workers", type=int, default=4, help="数据加载线程数")
    
    # 输出参数
    parser.add_argument("--output_dir", type=str, default="outputs", help="输出目录")
    parser.add_argument("--no_timestamp", action="store_true", help="不在输出目录中添加时间戳")
    
    args = parser.parse_args()
    
    main(args) 