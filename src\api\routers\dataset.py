"""
数据集管理API路由

提供数据集示例图片和对比展示的RESTful API接口
"""

from fastapi import APIRouter, HTTPException, status, Query
from fastapi.responses import JSONResponse

from ..models.dataset import (
    DatasetSampleRequest, DatasetSampleResponse,
    DatasetComparisonRequest, DatasetComparisonResponse,
    DatasetStatsResponse, DatasetInfoResponse
)
from ..services.dataset_service import dataset_service

router = APIRouter()

@router.get("/samples", response_model=DatasetSampleResponse)
async def get_dataset_samples(
    dataset_type: str = Query("old", description="数据集类型 (old/new)"),
    num_classes: int = Query(3, ge=1, le=10, description="选择的类别数量"),
    num_images_per_class: int = Query(3, ge=1, le=10, description="每个类别选择的图片数量"),
    include_base64: bool = Query(False, description="是否包含Base64编码的图片数据")
):
    """
    获取数据集示例图片
    
    Args:
        dataset_type: 数据集类型 ("old" 或 "new")
        num_classes: 选择的类别数量
        num_images_per_class: 每个类别选择的图片数量
        include_base64: 是否包含Base64编码的图片数据
    """
    try:
        image_infos, classes_found, message = dataset_service.get_dataset_samples(
            dataset_type=dataset_type,
            num_classes=num_classes,
            num_images_per_class=num_images_per_class,
            include_base64=include_base64
        )
        
        if not image_infos:
            return DatasetSampleResponse(
                success=False,
                dataset_type=dataset_type,
                images=[],
                total_images=0,
                classes_found=[],
                error=message
            )
        
        return DatasetSampleResponse(
            success=True,
            dataset_type=dataset_type,
            images=image_infos,
            total_images=len(image_infos),
            classes_found=classes_found,
            message=message
        )
        
    except Exception as e:
        return DatasetSampleResponse(
            success=False,
            dataset_type=dataset_type,
            images=[],
            total_images=0,
            classes_found=[],
            error=f"获取数据集示例失败: {str(e)}"
        )

@router.get("/comparison", response_model=DatasetComparisonResponse)
async def get_dataset_comparison(
    num_images_per_dataset: int = Query(4, ge=1, le=10, description="每个数据集选择的图片数量"),
    include_base64: bool = Query(False, description="是否包含Base64编码的图片数据"),
    specific_class: str = Query(None, description="指定对比的类别名称")
):
    """
    获取新旧数据集对比图片
    
    Args:
        num_images_per_dataset: 每个数据集选择的图片数量
        include_base64: 是否包含Base64编码的图片数据
        specific_class: 指定对比的类别名称
    """
    try:
        old_images, new_images, comparison_class, message = dataset_service.get_dataset_comparison(
            num_images_per_dataset=num_images_per_dataset,
            include_base64=include_base64,
            specific_class=specific_class
        )
        
        return DatasetComparisonResponse(
            success=True,
            old_dataset_images=old_images,
            new_dataset_images=new_images,
            comparison_class=comparison_class,
            old_dataset_count=len(old_images),
            new_dataset_count=len(new_images),
            message=message
        )
        
    except Exception as e:
        return DatasetComparisonResponse(
            success=False,
            old_dataset_images=[],
            new_dataset_images=[],
            comparison_class=None,
            old_dataset_count=0,
            new_dataset_count=0,
            error=f"获取数据集对比失败: {str(e)}"
        )

@router.get("/info/{dataset_type}", response_model=DatasetInfoResponse)
async def get_dataset_info(dataset_type: str):
    """
    获取指定数据集的详细信息
    
    Args:
        dataset_type: 数据集类型 ("old" 或 "new")
    """
    try:
        if dataset_type not in ["old", "new"]:
            return DatasetInfoResponse(
                success=False,
                error=f"不支持的数据集类型: {dataset_type}，仅支持 'old' 或 'new'"
            )
        
        dataset_info, message = dataset_service.get_dataset_info(dataset_type)
        
        return DatasetInfoResponse(
            success=True,
            dataset_info=dataset_info,
            message=message
        )
        
    except Exception as e:
        return DatasetInfoResponse(
            success=False,
            error=f"获取数据集信息失败: {str(e)}"
        )

@router.get("/stats", response_model=DatasetStatsResponse)
async def get_dataset_stats():
    """
    获取新旧数据集的统计信息
    """
    try:
        old_stats, new_stats, message = dataset_service.get_dataset_stats()
        
        return DatasetStatsResponse(
            success=True,
            old_dataset_stats=old_stats,
            new_dataset_stats=new_stats,
            message=message
        )
        
    except Exception as e:
        return DatasetStatsResponse(
            success=False,
            old_dataset_stats={},
            new_dataset_stats={},
            error=f"获取数据集统计失败: {str(e)}"
        )

@router.post("/samples", response_model=DatasetSampleResponse)
async def get_dataset_samples_post(request: DatasetSampleRequest):
    """
    获取数据集示例图片 (POST方式)
    
    Args:
        request: 数据集示例请求
    """
    try:
        image_infos, classes_found, message = dataset_service.get_dataset_samples(
            dataset_type=request.dataset_type,
            num_classes=request.num_classes,
            num_images_per_class=request.num_images_per_class,
            include_base64=request.include_base64
        )
        
        if not image_infos:
            return DatasetSampleResponse(
                success=False,
                dataset_type=request.dataset_type,
                images=[],
                total_images=0,
                classes_found=[],
                error=message
            )
        
        return DatasetSampleResponse(
            success=True,
            dataset_type=request.dataset_type,
            images=image_infos,
            total_images=len(image_infos),
            classes_found=classes_found,
            message=message
        )
        
    except Exception as e:
        return DatasetSampleResponse(
            success=False,
            dataset_type=request.dataset_type,
            images=[],
            total_images=0,
            classes_found=[],
            error=f"获取数据集示例失败: {str(e)}"
        )

@router.post("/comparison", response_model=DatasetComparisonResponse)
async def get_dataset_comparison_post(request: DatasetComparisonRequest):
    """
    获取新旧数据集对比图片 (POST方式)
    
    Args:
        request: 数据集对比请求
    """
    try:
        old_images, new_images, comparison_class, message = dataset_service.get_dataset_comparison(
            num_images_per_dataset=request.num_images_per_dataset,
            include_base64=request.include_base64,
            specific_class=request.specific_class
        )
        
        return DatasetComparisonResponse(
            success=True,
            old_dataset_images=old_images,
            new_dataset_images=new_images,
            comparison_class=comparison_class,
            old_dataset_count=len(old_images),
            new_dataset_count=len(new_images),
            message=message
        )
        
    except Exception as e:
        return DatasetComparisonResponse(
            success=False,
            old_dataset_images=[],
            new_dataset_images=[],
            comparison_class=None,
            old_dataset_count=0,
            new_dataset_count=0,
            error=f"获取数据集对比失败: {str(e)}"
        )
