# 遥感图像分类系统

基于深度学习的遥感图像场景分类系统，支持 21 个场景类别的自动识别，提供 Web 界面和 API 接口。

## 功能特性

- 支持多种深度学习模型（ResNet50、DenseNet201、ViT、Swin Transformer）
- 模型优化技术（剪枝、知识蒸馏、量化）
- 现代化 Web 界面，支持拖拽上传
- RESTful API 接口
- 自适应微调功能

## Docker 部署

### 构建镜像

```bash
docker build -t rsic:latest .
```

### 运行容器

```bash
docker run -d \
  --name rsic-app \
  -p 8000:8000 \
  --gpus all \
  -v $(pwd)/old_dataset:/app/old_dataset \
  -v $(pwd)/outputs:/app/outputs \
  rsic:latest
```

### 访问地址

- **Web 界面**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/health

## 数据集

支持 21 个遥感场景类别：
airplane, baseball_field, basketball_court, beach, bridge, chaparral, dense_residential, forest, freeway, golf_course, harbor, intersection, mobile_home_park, overpass, parking_lot, railway, river, runway, sparse_residential, storage_tank, tennis_court

数据集放置在 `old_dataset/` 目录下，按类别组织。

## 使用方法

### Web 界面

1. 访问 http://localhost:8000
2. 上传遥感图像
3. 选择预测模型
4. 查看分类结果

### API 调用

```python
import requests

# 图像预测
with open('image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/predict',
        files={'image': f},
        data={'model_key': 'resnet50'}
    )
    result = response.json()
    print(result['predictions'])
```

## 模型训练

### 自动化训练

```bash
# 训练单个模型
python src/main.py --model_names resnet50 --epochs 20

# 训练多个模型并优化
python src/main.py \
    --model_names resnet50 densenet201 \
    --pretrained \
    --epochs 20 \
    --optimize_mode all
```

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 启动Web应用
python src/web_app.py

# 访问应用
# - 主页: http://localhost:8000
# - API文档: http://localhost:8000/docs
# - 健康检查: http://localhost:8000/api/health
```

### 功能特性

- ✅ **图像分类预测**: 支持多种深度学习模型（ResNet50、DenseNet201、ViT、Swin Transformer）
- ✅ **模型优化对比**: 剪枝、蒸馏、量化等优化技术的性能对比
- ✅ **数据集管理**: 新旧数据集的可视化和对比分析
- ✅ **自适应微调**: 基于数据分布变化的自动模型微调
- ✅ **实时通信**: WebSocket 支持的实时状态更新和通知
- ✅ **响应式界面**: 现代化的扁平设计风格，支持多种交互方式

## 故障排除

### 模型和数据集检测问题 ✅ 已修复

**问题描述**: Web 应用启动时显示"无法检测到模型和数据集"，健康检查显示模型数量为 0

**修复内容**:

1. **修复全局变量引用问题**: 修改 `web_app.py` 中的模块导入方式

   ```python
   # 修改前：直接导入全局变量（在模块加载时为空）
   from app import MODEL_PATHS, CLASSES

   # 修改后：导入模块并动态访问全局变量
   import app as app_module
   # 使用 app_module.MODEL_PATHS 和 app_module.CLASSES
   ```

2. **确保模型发现功能正常工作**: 验证 `discover_available_models()` 函数能正确检测到：

   - 4 种模型类型：densenet201, resnet50, swin_t, vit_s_16
   - 3 种模型变体：原始、剪枝、蒸馏（共 12 个模型）
   - 21 个图像分类类别

3. **验证数据集加载功能**: 确认能正确加载 `old_dataset` 和 `new_dataset` 中的样本图片

**验证方法**: 运行诊断和测试脚本

```bash
# 诊断项目结构和文件
python diagnose_paths.py

# 测试模型加载功能
python test_model_loading.py

# 测试服务器连接
python test_server.py
```

**修复结果**:

- ✅ 成功检测到 12 个可用模型
- ✅ 成功加载 21 个图像类别
- ✅ 数据集样本加载正常
- ✅ 所有 API 端点工作正常

### 预测功能问题 ✅ 已修复

**问题描述**:

1. 点击开始预测无反应
2. WebSocket 连接被拒绝 (403 Forbidden)
3. 预测 API 返回 500 错误

**修复内容**:

1. **修复 JavaScript 重复函数定义**: 删除了重复的 `predictImage` 函数定义

2. **修复文件类型验证问题**: 改进 `/api/predict` 端点的文件验证逻辑

   ```python
   # 修复前：直接访问可能为None的content_type
   if not image.content_type.startswith('image/'):

   # 修复后：安全的类型检查
   content_type = image.content_type or ""
   if not content_type.startswith('image/'):
       # 备用验证：检查文件扩展名
   ```

3. **禁用 WebSocket 连接**: 移除导致 403 错误的 WebSocket 连接代码，改用轮询更新

4. **增强错误处理**: 添加更详细的错误信息和验证逻辑

5. **优化预测界面显示**:
   - 添加动画效果和过渡
   - 改进结果展示布局
   - 增强置信度条显示
   - 添加图像元数据显示

**验证方法**: 运行测试脚本

```bash
# 测试预测功能
python test_prediction.py

# 快速测试和启动服务器
python quick_test.py --server
```

**修复结果**:

- ✅ 预测功能完全正常
- ✅ 成功预测图像分类 (airplane: 100.00%)
- ✅ 推理时间显示正常 (551.28 ms)
- ✅ Web 界面响应正常
- ✅ 所有 API 端点工作正常

### WebSocket 连接问题 ✅ 已修复

**问题描述**: 日志持续输出 WebSocket 连接被拒绝错误

```
INFO:     ('127.0.0.1', 65390) - "WebSocket /ws" 403
INFO:     connection rejected (403 Forbidden)
INFO:     connection closed
```

**问题分析**:

1. 有客户端代码尝试连接到 `/ws` WebSocket 端点
2. FastAPI 服务器没有定义该端点，导致 403 Forbidden 错误
3. 虽然 JavaScript 中 WebSocket 连接已被禁用，但可能有其他地方仍在尝试连接

**修复内容**:

1. **添加 WebSocket 端点**: 在 `src/web_app.py` 中添加完整的 WebSocket 支持

   ```python
   # 添加 WebSocket 连接管理器
   class ConnectionManager:
       def __init__(self):
           self.active_connections: List[WebSocket] = []

   # 添加 WebSocket 端点
   @app.websocket("/ws")
   async def websocket_endpoint(websocket: WebSocket):
       # 处理 WebSocket 连接和消息
   ```

2. **重新启用前端 WebSocket 连接**: 修改 `static/js/main.js` 中的 WebSocket 实现

   ```javascript
   function setupWebSocketConnection() {
     const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
     const wsUrl = `${protocol}//${window.location.host}/ws`;
     const ws = new WebSocket(wsUrl);
     // 完整的连接处理逻辑
   }
   ```

3. **增强消息处理**: 支持多种消息类型

   - `connection_established`: 连接建立通知
   - `status_update`: 状态更新
   - `system_alert`: 系统警报
   - `heartbeat`: 心跳保持连接
   - `ping/pong`: 连接测试

4. **自动重连机制**: 连接断开后自动尝试重连

**验证方法**: 运行 WebSocket 测试

```bash
# 启动服务器
python src/web_app.py

# 在另一个终端测试WebSocket连接
python test_websocket.py

# 测试多个连接
python test_websocket.py --multi
```

**修复结果**:

- ✅ WebSocket 403 错误完全消除
- ✅ 实时连接功能正常工作
- ✅ 支持多个并发 WebSocket 连接
- ✅ 支持自动重连和心跳检测
- ✅ 消息类型完整支持（ping/pong、状态更新、系统警报等）
- ✅ 为将来的实时功能做好准备

**技术细节**:

- WebSocket 端点: `ws://localhost:8000/ws`
- 支持的消息类型: `connection_established`, `status_update`, `system_alert`, `heartbeat`, `ping/pong`
- 自动重连间隔: 5 秒
- 连接管理: 支持多客户端并发连接

### 前端资源加载失败 ✅ 已修复

**问题描述**: 前端页面显示失败，出现 CSS、JS 文件 404 错误

**修复内容**:

1. **修复 HTML 资源路径**: 将相对路径改为绝对路径

   - `css/style.css` → `/static/css/style.css`
   - `js/main.js` → `/static/js/main.js`

2. **修复静态文件挂载路径**: 更新 web_app.py 中的静态目录路径

   ```python
   static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
   app.mount("/static", StaticFiles(directory=static_dir), name="static")
   ```

3. **添加 favicon**: 使用 SVG emoji 避免 favicon.ico 404 错误

**验证方法**: 运行测试脚本

```bash
python test_frontend.py
```

### 文件结构确认

确认以下文件结构正确：

```
项目根目录/
├── src/
│   └── web_app.py
├── static/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js
│   └── index.html
└── test_frontend.py
```

### 常见问题

- **404 错误**: 检查静态文件路径和文件是否存在
- **CORS 错误**: 确认 CORS 中间件已正确配置
- **模型加载失败**: 确认模型文件在 outputs 目录中
- **端口占用**: 如果 8000 端口被占用，修改 web_app.py 中的端口号
