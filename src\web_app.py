"""
FastAPI Web Application for Remote Sensing Image Classification
Provides HTML/CSS/JS frontend and API endpoints
"""

import os
import json
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from PIL import Image
import io

# Import existing functions from app.py
from app import (
    discover_available_models,
    load_model,
    predict,
    create_pruning_comparison,
    create_distillation_comparison,
    create_quantization_comparison,
    get_dataset_comparison_images,
    start_adaptive_monitoring,
    stop_adaptive_monitoring,
    manual_fine_tune,
    get_fine_tuning_status,
    check_data_distribution
)
# Import app module to access global variables dynamically
import app as app_module

# Initialize FastAPI app
app = FastAPI(
    title="遥感图像分类系统",
    description="Remote Sensing Image Classification System with Model Optimization",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
import os
static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Initialize models on startup
@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup"""
    print("Initializing Remote Sensing Image Classification System...")
    
    # Discover available models
    run_dir = discover_available_models()
    if run_dir:
        print(f"Models loaded from: {run_dir}")
        print(f"Available models: {list(app_module.MODEL_PATHS.keys())}")
    else:
        print("Warning: No models found")
    
    print("System ready!")

# Root endpoint - serve the main HTML page
@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main HTML page"""
    try:
        html_path = os.path.join(static_dir, "index.html")
        with open(html_path, "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Frontend not found")

# API Endpoints

@app.get("/api/models")
async def get_available_models():
    """Get list of available models"""
    models = []
    for key, info in app_module.MODEL_PATHS.items():
        models.append({
            "key": key,
            "name": key,
            "type": info["type"],
            "variant": info["variant"]
        })

    return {"models": models}

@app.post("/api/predict")
async def predict_image(
    image: UploadFile = File(...),
    model_key: str = Form(...)
):
    """Predict image classification"""
    try:
        # Validate file type
        content_type = image.content_type or ""
        if not content_type.startswith('image/'):
            # Try to validate by filename extension
            filename = image.filename or ""
            valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp')
            if not filename.lower().endswith(valid_extensions):
                raise HTTPException(status_code=400, detail="Invalid file type. Please upload an image file.")

        # Read image
        image_data = await image.read()

        # Validate image data
        if len(image_data) == 0:
            raise HTTPException(status_code=400, detail="Empty file uploaded")

        try:
            pil_image = Image.open(io.BytesIO(image_data))
        except Exception as img_error:
            raise HTTPException(status_code=400, detail=f"Invalid image file: {str(img_error)}")

        # Convert to RGB if necessary
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')

        # Validate model key
        if not model_key or model_key not in app_module.MODEL_PATHS:
            available_models = list(app_module.MODEL_PATHS.keys())
            raise HTTPException(
                status_code=400,
                detail=f"Invalid model key. Available models: {available_models}"
            )

        # Predict using existing function
        results, time_info = predict(pil_image, model_key)

        if isinstance(results, dict):
            return {
                "predictions": results,
                "inference_time": time_info,
                "status": "success"
            }
        else:
            raise HTTPException(status_code=500, detail=str(results))

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/api/comparison/{comparison_type}/{model_type}")
async def get_comparison_table(comparison_type: str, model_type: str):
    """Get model comparison table"""
    try:
        if comparison_type == "pruning":
            html_content = create_pruning_comparison(model_type)
        elif comparison_type == "distillation":
            html_content = create_distillation_comparison(model_type)
        elif comparison_type == "quantization":
            html_content = create_quantization_comparison(model_type)
        else:
            raise HTTPException(status_code=400, detail="Invalid comparison type")
        
        return {"html": html_content}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")

@app.get("/api/dataset/samples")
async def get_dataset_samples():
    """Get dataset sample images"""
    try:
        old_images, old_info, new_images, new_info = get_dataset_comparison_images()
        
        return {
            "old_images": old_images,
            "old_info": old_info,
            "new_images": new_images,
            "new_info": new_info
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Dataset loading failed: {str(e)}")

@app.get("/api/dataset/image/{image_path:path}")
async def get_dataset_image(image_path: str):
    """Serve dataset images"""
    try:
        # Security check - ensure path is within project directory
        if ".." in image_path or image_path.startswith("/"):
            raise HTTPException(status_code=400, detail="Invalid path")
        
        if os.path.exists(image_path):
            return FileResponse(image_path)
        else:
            raise HTTPException(status_code=404, detail="Image not found")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image serving failed: {str(e)}")

# Adaptive Fine-tuning Endpoints

@app.post("/api/adaptive/start")
async def start_monitoring():
    """Start adaptive monitoring"""
    try:
        message = start_adaptive_monitoring()
        return {"message": message, "status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Start monitoring failed: {str(e)}")

@app.post("/api/adaptive/stop")
async def stop_monitoring():
    """Stop adaptive monitoring"""
    try:
        message = stop_adaptive_monitoring()
        return {"message": message, "status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Stop monitoring failed: {str(e)}")

@app.post("/api/adaptive/manual-finetune")
async def trigger_manual_finetune():
    """Trigger manual fine-tuning"""
    try:
        message = manual_fine_tune()
        return {"message": message, "status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Manual fine-tune failed: {str(e)}")

@app.get("/api/adaptive/status")
async def get_adaptive_status():
    """Get adaptive fine-tuning status"""
    try:
        status = get_fine_tuning_status()
        return {"fine_tuning_status": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")

@app.get("/api/adaptive/check-distribution")
async def check_distribution():
    """Check data distribution"""
    try:
        needs_fine_tuning, diff_score = check_data_distribution()
        return {
            "needs_fine_tuning": needs_fine_tuning,
            "difference_score": diff_score,
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Distribution check failed: {str(e)}")

# Health check endpoint
@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "models_loaded": len(app_module.MODEL_PATHS),
        "classes_count": len(app_module.CLASSES)
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"detail": "Resource not found"}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

if __name__ == "__main__":
    # Run the application
    print("Starting Remote Sensing Image Classification Web Application...")
    print("Access the application at: http://localhost:8000")
    print("API documentation at: http://localhost:8000/docs")
    
    uvicorn.run(
        "web_app:app",
        host="localhost",
        port=8000,
        reload=True,
        log_level="info"
    )
