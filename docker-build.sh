#!/bin/bash
# =============================================================================
# Docker Build Script for Remote Sensing Image Classification System
# Provides easy commands for building and running the application
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="remote-sensing-classification"
TAG="latest"
TARGET="production"
PLATFORM="linux/amd64"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build-prod     Build production image"
    echo "  build-dev      Build development image"
    echo "  build-gpu      Build GPU-enabled image"
    echo "  run-prod       Run production container"
    echo "  run-dev        Run development container"
    echo "  run-gpu        Run GPU container"
    echo "  stop           Stop all containers"
    echo "  clean          Remove all images and containers"
    echo "  logs           Show container logs"
    echo "  health         Check container health"
    echo "  shell          Open shell in running container"
    echo ""
    echo "Options:"
    echo "  --tag TAG      Set image tag (default: latest)"
    echo "  --no-cache     Build without cache"
    echo "  --help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build-prod --tag v1.0.0"
    echo "  $0 run-dev"
    echo "  $0 logs"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to build image
build_image() {
    local target=$1
    local tag_suffix=$2
    
    print_info "Building ${target} image..."
    
    local build_args=""
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="--no-cache"
    fi
    
    docker build \
        ${build_args} \
        --target ${target} \
        --platform ${PLATFORM} \
        --tag ${IMAGE_NAME}:${TAG}${tag_suffix} \
        --tag ${IMAGE_NAME}:${target} \
        .
    
    print_success "${target} image built successfully!"
}

# Function to run container
run_container() {
    local service=$1
    
    print_info "Starting ${service} container..."
    
    docker-compose up -d ${service}
    
    print_success "${service} container started!"
    print_info "Access the application at: http://localhost:8000"
    
    if [[ "$service" == "app-dev" ]]; then
        print_info "Jupyter Lab available at: http://localhost:8888"
        print_info "TensorBoard available at: http://localhost:6006"
    fi
}

# Function to show logs
show_logs() {
    local service=${1:-app}
    
    print_info "Showing logs for ${service}..."
    docker-compose logs -f ${service}
}

# Function to check health
check_health() {
    local container=${1:-rsic-app}
    
    print_info "Checking health of ${container}..."
    
    if docker ps --filter "name=${container}" --filter "status=running" | grep -q ${container}; then
        local health=$(docker inspect --format='{{.State.Health.Status}}' ${container} 2>/dev/null || echo "no-healthcheck")
        
        if [[ "$health" == "healthy" ]]; then
            print_success "Container ${container} is healthy"
        elif [[ "$health" == "unhealthy" ]]; then
            print_error "Container ${container} is unhealthy"
            docker logs --tail 20 ${container}
        else
            print_warning "Container ${container} has no health check or is starting"
        fi
    else
        print_error "Container ${container} is not running"
    fi
}

# Function to open shell
open_shell() {
    local container=${1:-rsic-app}
    
    print_info "Opening shell in ${container}..."
    
    if docker ps --filter "name=${container}" --filter "status=running" | grep -q ${container}; then
        docker exec -it ${container} /bin/bash
    else
        print_error "Container ${container} is not running"
        exit 1
    fi
}

# Function to stop containers
stop_containers() {
    print_info "Stopping all containers..."
    docker-compose down
    print_success "All containers stopped!"
}

# Function to clean up
cleanup() {
    print_warning "This will remove all images and containers. Are you sure? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_info "Stopping containers..."
        docker-compose down --volumes --remove-orphans
        
        print_info "Removing images..."
        docker rmi $(docker images ${IMAGE_NAME} -q) 2>/dev/null || true
        
        print_info "Cleaning up unused resources..."
        docker system prune -f
        
        print_success "Cleanup completed!"
    else
        print_info "Cleanup cancelled."
    fi
}

# Parse command line arguments
NO_CACHE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --tag)
            TAG="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        build-prod)
            COMMAND="build-prod"
            shift
            ;;
        build-dev)
            COMMAND="build-dev"
            shift
            ;;
        build-gpu)
            COMMAND="build-gpu"
            shift
            ;;
        run-prod)
            COMMAND="run-prod"
            shift
            ;;
        run-dev)
            COMMAND="run-dev"
            shift
            ;;
        run-gpu)
            COMMAND="run-gpu"
            shift
            ;;
        stop)
            COMMAND="stop"
            shift
            ;;
        clean)
            COMMAND="clean"
            shift
            ;;
        logs)
            COMMAND="logs"
            shift
            ;;
        health)
            COMMAND="health"
            shift
            ;;
        shell)
            COMMAND="shell"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if command is provided
if [[ -z "${COMMAND:-}" ]]; then
    print_error "No command provided."
    show_usage
    exit 1
fi

# Check Docker
check_docker

# Execute command
case $COMMAND in
    build-prod)
        build_image "production" ""
        ;;
    build-dev)
        build_image "development" "-dev"
        ;;
    build-gpu)
        build_image "production" "-gpu"
        ;;
    run-prod)
        run_container "app"
        ;;
    run-dev)
        run_container "app-dev"
        ;;
    run-gpu)
        run_container "app-gpu"
        ;;
    stop)
        stop_containers
        ;;
    clean)
        cleanup
        ;;
    logs)
        show_logs
        ;;
    health)
        check_health
        ;;
    shell)
        open_shell
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
