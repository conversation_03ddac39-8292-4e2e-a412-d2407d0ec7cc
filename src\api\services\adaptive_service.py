"""
自适应微调业务逻辑服务

复用app.py中的自适应微调功能，提供API服务层
"""

import os
import sys
import datetime
from typing import Tuple, Optional, Dict, Any

# 添加父目录到路径以导入app.py中的函数
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入app.py中的自适应微调功能
from app import (
    ADAPTIVE_MONITORING, LAST_CHECK_TIME, DISTRIBUTION_THRESHOLD, 
    FINE_TUNING_STATUS, OLD_FEATURES, NEW_FEATURES,
    start_adaptive_monitoring, stop_adaptive_monitoring,
    manual_fine_tune, get_fine_tuning_status,
    check_data_distribution, fine_tune_model
)

from ..config import settings

class AdaptiveService:
    """自适应微调服务类"""
    
    def __init__(self):
        """初始化自适应微调服务"""
        pass
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        global ADAPTIVE_MONITORING, LAST_CHECK_TIME, DISTRIBUTION_THRESHOLD, FINE_TUNING_STATUS
        
        # 格式化最后检查时间
        last_check_str = None
        if LAST_CHECK_TIME:
            last_check_str = LAST_CHECK_TIME.strftime("%Y-%m-%d %H:%M:%S")
        
        return {
            "monitoring_active": ADAPTIVE_MONITORING,
            "fine_tuning_running": FINE_TUNING_STATUS.get("running", False),
            "fine_tuning_message": FINE_TUNING_STATUS.get("message", "待命中"),
            "last_check_time": last_check_str,
            "distribution_threshold": DISTRIBUTION_THRESHOLD
        }
    
    def start_monitoring(self) -> str:
        """启动自适应监控"""
        try:
            result = start_adaptive_monitoring()
            return result
        except Exception as e:
            return f"启动监控失败: {str(e)}"
    
    def stop_monitoring(self) -> str:
        """停止自适应监控"""
        try:
            result = stop_adaptive_monitoring()
            return result
        except Exception as e:
            return f"停止监控失败: {str(e)}"
    
    def manual_tune(self, model_type: str = "resnet50", epochs: int = 5, learning_rate: float = 1e-4) -> str:
        """手动触发微调"""
        try:
            # 注意：app.py中的manual_fine_tune函数不接受参数，使用默认配置
            result = manual_fine_tune()
            return result
        except Exception as e:
            return f"手动微调失败: {str(e)}"
    
    def check_distribution(self, threshold: Optional[float] = None) -> Tuple[bool, float, float, Optional[int], Optional[int], str]:
        """
        检查数据分布
        
        Args:
            threshold: 自定义阈值，如果为None则使用全局阈值
            
        Returns:
            (是否需要微调, 差异分数, 使用的阈值, 旧数据特征数量, 新数据特征数量, 消息)
        """
        global DISTRIBUTION_THRESHOLD, OLD_FEATURES, NEW_FEATURES
        
        try:
            # 如果提供了自定义阈值，临时更新全局阈值
            original_threshold = DISTRIBUTION_THRESHOLD
            if threshold is not None:
                DISTRIBUTION_THRESHOLD = threshold
            
            # 执行分布检查
            needs_fine_tuning, diff_score = check_data_distribution()
            
            # 获取特征数量
            old_count = len(OLD_FEATURES) if OLD_FEATURES is not None else None
            new_count = len(NEW_FEATURES) if NEW_FEATURES is not None else None
            
            # 生成消息
            if needs_fine_tuning:
                message = f"检测到分布差异: {diff_score:.4f} > {DISTRIBUTION_THRESHOLD:.4f}，建议微调"
            else:
                message = f"分布差异: {diff_score:.4f} <= {DISTRIBUTION_THRESHOLD:.4f}，无需微调"
            
            # 恢复原始阈值
            if threshold is not None:
                DISTRIBUTION_THRESHOLD = original_threshold
            
            return needs_fine_tuning, diff_score, DISTRIBUTION_THRESHOLD, old_count, new_count, message
            
        except Exception as e:
            # 恢复原始阈值
            if threshold is not None:
                DISTRIBUTION_THRESHOLD = original_threshold
            return False, 0.0, DISTRIBUTION_THRESHOLD, None, None, f"检查分布失败: {str(e)}"
    
    def update_threshold(self, new_threshold: float) -> Tuple[bool, float, float, str]:
        """
        更新分布差异阈值
        
        Args:
            new_threshold: 新的阈值
            
        Returns:
            (是否成功, 旧阈值, 新阈值, 消息)
        """
        global DISTRIBUTION_THRESHOLD
        
        try:
            old_threshold = DISTRIBUTION_THRESHOLD
            DISTRIBUTION_THRESHOLD = new_threshold
            
            message = f"分布差异阈值已从 {old_threshold:.4f} 更新为 {new_threshold:.4f}"
            return True, old_threshold, new_threshold, message
            
        except Exception as e:
            return False, DISTRIBUTION_THRESHOLD, new_threshold, f"更新阈值失败: {str(e)}"
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        global DISTRIBUTION_THRESHOLD
        
        return {
            "distribution_threshold": DISTRIBUTION_THRESHOLD,
            "monitoring_interval": settings.MONITORING_INTERVAL,
            "fine_tune_epochs": settings.FINE_TUNE_EPOCHS,
            "distill_epochs": settings.DISTILL_EPOCHS,
            "old_dataset_dir": str(settings.OLD_DATASET_DIR),
            "new_dataset_dir": str(settings.NEW_DATASET_DIR),
            "checkpoints_dir": str(settings.CHECKPOINTS_DIR)
        }
    
    def get_fine_tuning_status(self) -> str:
        """获取微调状态"""
        try:
            return get_fine_tuning_status()
        except Exception as e:
            return f"获取状态失败: {str(e)}"

# 全局自适应服务实例
adaptive_service = AdaptiveService()
