#!/usr/bin/env python3
"""
快速测试脚本
"""

import os
import sys

# 添加src目录到Python路径
src_path = os.path.join(os.getcwd(), 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

def test_imports():
    """测试导入"""
    print("=== 测试导入 ===")
    
    try:
        print("1. 测试app模块导入...")
        from app import discover_available_models, MODEL_PATHS, CLASSES
        print("   ✅ app模块导入成功")
        
        print("2. 测试模型发现...")
        run_dir = discover_available_models()
        if run_dir:
            print(f"   ✅ 模型发现成功: {run_dir}")
            print(f"   ✅ 发现 {len(MODEL_PATHS)} 个模型")
            print(f"   ✅ 加载 {len(CLASSES)} 个类别")
        else:
            print("   ❌ 模型发现失败")
        
        print("3. 测试web_app模块导入...")
        from web_app import app
        print("   ✅ web_app模块导入成功")
        
        print("4. 测试FastAPI应用...")
        from fastapi.testclient import TestClient
        client = TestClient(app)
        
        # 测试健康检查
        response = client.get("/api/health")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 健康检查成功: {data}")
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
        
        # 测试模型列表
        response = client.get("/api/models")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 模型列表成功: {len(data.get('models', []))} 个模型")
        else:
            print(f"   ❌ 模型列表失败: {response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_start():
    """测试服务器启动"""
    print("\n=== 测试服务器启动 ===")
    
    try:
        import uvicorn
        from web_app import app
        
        print("正在启动服务器...")
        print("访问地址: http://localhost:8000")
        print("按 Ctrl+C 停止服务器")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="快速测试脚本")
    parser.add_argument("--server", action="store_true", help="启动服务器")
    args = parser.parse_args()
    
    if args.server:
        if test_imports():
            test_server_start()
    else:
        test_imports()
