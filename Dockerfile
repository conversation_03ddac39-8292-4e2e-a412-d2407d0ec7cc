# =============================================================================
# Multi-stage Dockerfile for Remote Sensing Image Classification System
# Optimized for production deployment with security and performance best practices
# =============================================================================

# Build arguments for flexibility
ARG PYTHON_VERSION=3.10
ARG CUDA_VERSION=12.2.2
ARG UBUNTU_VERSION=22.04
ARG APP_USER=appuser
ARG APP_UID=1000
ARG APP_GID=1000

# =============================================================================
# Stage 1: Base Image with System Dependencies
# =============================================================================
FROM nvidia/cuda:${CUDA_VERSION}-runtime-ubuntu${UBUNTU_VERSION} as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    TORCH_CUDA_ARCH_LIST="6.0 6.1 7.0 7.5 8.0 8.6+PTX" \
    FORCE_CUDA="1"

# Install system dependencies in a single layer
RUN apt-get update && apt-get install -y --no-install-recommends \
    python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-dev \
    python3-pip \
    python3-venv \
    build-essential \
    curl \
    wget \
    git \
    ca-certificates \
    # OpenCV dependencies
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    # Additional image processing libraries
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    # Cleanup in same layer
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && apt-get autoremove -y

# Create symbolic links for python
RUN ln -sf /usr/bin/python${PYTHON_VERSION} /usr/bin/python3 \
    && ln -sf /usr/bin/python3 /usr/bin/python

# Upgrade pip and install essential tools
RUN python -m pip install --no-cache-dir --upgrade pip setuptools wheel

# =============================================================================
# Stage 2: Dependencies Installation
# =============================================================================
FROM base AS dependencies

# Create application user for security
RUN groupadd -g ${APP_GID} ${APP_USER} \
    && useradd -u ${APP_UID} -g ${APP_GID} -m -s /bin/bash ${APP_USER}

# Set working directory
WORKDIR /app

# Copy dependency files first for better caching
COPY requirements.txt pyproject.toml ./

# Install PyTorch with CUDA support first (largest dependency)
RUN pip install --no-cache-dir torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Create a script to filter production dependencies
RUN echo '#!/usr/bin/env python3\n\
    import sys\n\
    \n\
    # Core dependencies for production\n\
    core_deps = [\n\
    "torch>=2.0.0",\n\
    "torchvision>=0.15.0",\n\
    "numpy>=1.24.0",\n\
    "pandas>=2.0.0",\n\
    "scikit-learn>=1.3.0",\n\
    "matplotlib>=3.7.0",\n\
    "Pillow>=10.0.0",\n\
    "tqdm>=4.65.0",\n\
    "fastapi>=0.100.0",\n\
    "uvicorn[standard]>=0.23.0",\n\
    "python-multipart>=0.0.6",\n\
    "jinja2>=3.1.0",\n\
    "aiofiles>=23.0.0",\n\
    "websockets>=11.0.0",\n\
    "timm>=0.9.0",\n\
    "pydantic>=2.0.0",\n\
    "python-dotenv>=1.0.0",\n\
    "loguru>=0.7.0",\n\
    "rich>=13.0.0",\n\
    "click>=8.1.0",\n\
    "requests>=2.31.0",\n\
    "httpx>=0.24.0",\n\
    "h5py>=3.9.0",\n\
    "scipy>=1.11.0",\n\
    "imageio>=2.31.0",\n\
    "opencv-python>=4.8.0",\n\
    "gunicorn>=21.0.0"\n\
    ]\n\
    \n\
    with open("requirements-prod.txt", "w") as f:\n\
    for dep in core_deps:\n\
    f.write(dep + "\\n")\n\
    ' > create_prod_requirements.py && python create_prod_requirements.py

# Install production dependencies
RUN pip install --no-cache-dir -r requirements-prod.txt \
    && pip cache purge

# Install the package in editable mode
RUN pip install --no-cache-dir -e .

# =============================================================================
# Stage 3: Development Image
# =============================================================================
FROM dependencies AS development

# Install development dependencies
RUN pip install --no-cache-dir \
    pytest>=7.4.0 \
    pytest-asyncio>=0.21.0 \
    pytest-cov>=4.1.0 \
    black>=23.7.0 \
    isort>=5.12.0 \
    flake8>=6.0.0 \
    mypy>=1.5.0 \
    jupyter>=1.0.0 \
    ipython \
    notebook \
    jupyterlab \
    pre-commit>=3.3.0

# Copy source code
COPY --chown=${APP_USER}:${APP_USER} . /app/

# Create necessary directories with proper permissions
RUN mkdir -p /app/data /app/outputs /app/logs /app/static /app/imgs \
    && chown -R ${APP_USER}:${APP_USER} /app \
    && chmod -R 755 /app

# Switch to non-root user
USER ${APP_USER}

# Expose ports for development (web app, jupyter, tensorboard)
EXPOSE 8000 8888 6006

# Set development environment variables
ENV ENVIRONMENT=development \
    DEBUG=true \
    RELOAD=true \
    LOG_LEVEL=DEBUG

# Development command with hot reload
CMD ["uvicorn", "src.web_app:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

# =============================================================================
# Stage 4: Production Image
# =============================================================================
FROM dependencies AS production

# Copy only necessary files for production
COPY --chown=${APP_USER}:${APP_USER} src/ /app/src/
COPY --chown=${APP_USER}:${APP_USER} static/ /app/static/
COPY --chown=${APP_USER}:${APP_USER} .env.example /app/.env.example

# Create necessary directories with proper permissions
RUN mkdir -p /app/data /app/outputs /app/logs /app/imgs \
    && chown -R ${APP_USER}:${APP_USER} /app \
    && chmod -R 755 /app

# Create health check script with better error handling
RUN echo '#!/bin/bash\n\
    set -e\n\
    # Check if the application is responding\n\
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/health || echo "000")\n\
    if [ "$response" = "200" ]; then\n\
    echo "Health check passed"\n\
    exit 0\n\
    else\n\
    echo "Health check failed with status: $response"\n\
    exit 1\n\
    fi' > /app/healthcheck.sh \
    && chmod +x /app/healthcheck.sh \
    && chown ${APP_USER}:${APP_USER} /app/healthcheck.sh

# Create startup script for better process management
RUN echo '#!/bin/bash\n\
    set -e\n\
    echo "Starting Remote Sensing Image Classification System..."\n\
    echo "Environment: $ENVIRONMENT"\n\
    echo "Debug mode: $DEBUG"\n\
    echo "Log level: $LOG_LEVEL"\n\
    \n\
    # Check if models directory exists\n\
    if [ ! -d "/app/outputs/checkpoints" ]; then\n\
    echo "Warning: No model checkpoints found in /app/outputs/checkpoints"\n\
    fi\n\
    \n\
    # Start the application\n\
    exec gunicorn src.web_app:app \\\n\
    --workers ${WORKERS:-4} \\\n\
    --worker-class uvicorn.workers.UvicornWorker \\\n\
    --bind 0.0.0.0:8000 \\\n\
    --timeout ${TIMEOUT:-120} \\\n\
    --keep-alive ${KEEP_ALIVE:-5} \\\n\
    --max-requests ${MAX_REQUESTS:-1000} \\\n\
    --max-requests-jitter ${MAX_REQUESTS_JITTER:-100} \\\n\
    --preload \\\n\
    --log-level ${LOG_LEVEL,,} \\\n\
    --access-logfile - \\\n\
    --error-logfile -' > /app/start.sh \
    && chmod +x /app/start.sh \
    && chown ${APP_USER}:${APP_USER} /app/start.sh

# Switch to non-root user
USER ${APP_USER}

# Set environment variables for production
ENV ENVIRONMENT=production \
    DEBUG=false \
    RELOAD=false \
    LOG_LEVEL=INFO \
    WORKERS=4 \
    TIMEOUT=120 \
    KEEP_ALIVE=5 \
    MAX_REQUESTS=1000 \
    MAX_REQUESTS_JITTER=100

# Expose application port
EXPOSE 8000

# Add health check with longer intervals for production
HEALTHCHECK --interval=60s --timeout=30s --start-period=60s --retries=3 \
    CMD /app/healthcheck.sh

# Production command using startup script
CMD ["/app/start.sh"]