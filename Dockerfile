# =============================================================================
# Multi-stage Dockerfile for Remote Sensing Image Classification System
# Optimized for production deployment with security and performance best practices
# =============================================================================

# Build arguments for flexibility
ARG PYTHON_VERSION=3.10
ARG CUDA_VERSION=12.2.2
ARG UBUNTU_VERSION=22.04
ARG APP_USER=appuser
ARG APP_UID=1000
ARG APP_GID=1000

# =============================================================================
# Stage 1: Base Image with System Dependencies
# =============================================================================
FROM nvidia/cuda:${CUDA_VERSION}-base-ubuntu${UBUNTU_VERSION} as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-dev \
    python3-pip \
    python3-venv \
    build-essential \
    curl \
    wget \
    git \
    ca-certificates \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create symbolic links for python
RUN ln -sf /usr/bin/python${PYTHON_VERSION} /usr/bin/python3 \
    && ln -sf /usr/bin/python3 /usr/bin/python

# Upgrade pip and install essential tools
RUN python -m pip install --upgrade pip setuptools wheel

# =============================================================================
# Stage 2: Dependencies Installation
# =============================================================================
FROM base as dependencies

# Create application user for security
RUN groupadd -g ${APP_GID} ${APP_USER} \
    && useradd -u ${APP_UID} -g ${APP_GID} -m -s /bin/bash ${APP_USER}

# Set working directory
WORKDIR /app

# Copy dependency files
COPY requirements.txt requirements-dev.txt pyproject.toml setup.py setup.cfg ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir -e . \
    && pip cache purge

# =============================================================================
# Stage 3: Development Image
# =============================================================================
FROM dependencies as development

# Install development dependencies
RUN pip install --no-cache-dir -r requirements-dev.txt

# Install additional development tools
RUN pip install --no-cache-dir \
    jupyter \
    ipython \
    notebook \
    jupyterlab

# Copy source code
COPY --chown=${APP_USER}:${APP_USER} . /app/

# Create necessary directories
RUN mkdir -p /app/data /app/outputs /app/logs /app/static \
    && chown -R ${APP_USER}:${APP_USER} /app

# Switch to non-root user
USER ${APP_USER}

# Expose ports for development
EXPOSE 8000 8888 6006

# Development command
CMD ["uvicorn", "src.web_app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# =============================================================================
# Stage 4: Production Image
# =============================================================================
FROM dependencies as production

# Copy only necessary files for production
COPY --chown=${APP_USER}:${APP_USER} src/ /app/src/
COPY --chown=${APP_USER}:${APP_USER} static/ /app/static/
COPY --chown=${APP_USER}:${APP_USER} .env.example /app/.env.example

# Create necessary directories with proper permissions
RUN mkdir -p /app/data /app/outputs /app/logs \
    && chown -R ${APP_USER}:${APP_USER} /app \
    && chmod -R 755 /app

# Create health check script
RUN echo '#!/bin/bash\ncurl -f http://localhost:8000/api/health || exit 1' > /app/healthcheck.sh \
    && chmod +x /app/healthcheck.sh \
    && chown ${APP_USER}:${APP_USER} /app/healthcheck.sh

# Switch to non-root user
USER ${APP_USER}

# Set environment variables for production
ENV ENVIRONMENT=production \
    DEBUG=false \
    RELOAD=false \
    LOG_LEVEL=INFO

# Expose application port
EXPOSE 8000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD /app/healthcheck.sh

# Production command with gunicorn for better performance
CMD ["gunicorn", "src.web_app:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]