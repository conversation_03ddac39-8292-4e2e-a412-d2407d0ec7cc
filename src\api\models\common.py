"""
通用Pydantic模型定义

包含API通用的请求和响应模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field

class APIResponse(BaseModel):
    """通用API响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    error: Optional[str] = Field(None, description="错误信息")

class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    message: str = Field(..., description="状态消息")

class APIInfoResponse(BaseModel):
    """API信息响应模型"""
    name: str = Field(..., description="API名称")
    version: str = Field(..., description="API版本")
    description: str = Field(..., description="API描述")
    endpoints: Dict[str, str] = Field(..., description="可用端点")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    detail: Optional[str] = Field(None, description="错误详情")

class ModelInfo(BaseModel):
    """模型信息模型"""
    name: str = Field(..., description="模型名称")
    type: str = Field(..., description="模型类型")
    variant: str = Field(..., description="模型变体")
    path: str = Field(..., description="模型路径")
    size_mb: float = Field(..., description="模型大小(MB)")
    device: str = Field(..., description="运行设备")

class MetricsInfo(BaseModel):
    """评估指标模型"""
    accuracy: float = Field(..., description="准确率")
    precision: float = Field(..., description="精确率")
    recall: float = Field(..., description="召回率")
    f1: float = Field(..., description="F1分数")
    parameter_count: Optional[int] = Field(None, description="参数数量")
