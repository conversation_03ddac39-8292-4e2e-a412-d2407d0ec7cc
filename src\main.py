import os
import argparse
import traceback
import matplotlib.pyplot as plt
from typing import List, Dict
from copy import deepcopy
import datetime

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader

from data.dataset import create_dataloaders
from models.base_model import create_model, get_model_info
from utils.metrics import count_parameters
from train import train_model
from evaluate import evaluate_model

from optimization.distillation import train_with_distillation, create_student_model
# 从pruning模块导入必要函数
from optimization.pruning import (
    prune_model_global, 
    prune_model_l1_structured, 
    prune_model_l1_unstructured,
    calculate_sparsity
)
from prune import fine_tune #


def print_section(title):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 60) # 更宽的分隔线
    print(f" {title} ".center(60, "-"))
    print("=" * 60 + "\n")


def main(args):
    try:
        # 创建基础输出目录
        base_output_dir = args.output_dir
        os.makedirs(base_output_dir, exist_ok=True)
    
    
        global_run_output_dir = os.path.join(base_output_dir, "checkpoints")
        os.makedirs(global_run_output_dir, exist_ok=True)
        print(f"全局运行输出将保存在: {global_run_output_dir}")
        
        # 记录全局命令行参数
        with open(os.path.join(global_run_output_dir, "global_args.txt"), "w") as f:
            for arg, value in vars(args).items():
                f.write(f"{arg}: {value}\n")
        
        # 设置设备
        device = torch.device(args.device if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")
        
        # --- 数据加载 (只需一次) ---
        print_section("数据加载")
        print(f"数据目录: {args.data_dir}")
        try:
            train_loader, val_loader, test_loader, classes = create_dataloaders(
                data_dir=args.data_dir,
                batch_size=args.batch_size,
                num_workers=args.num_workers
            )
            print(f"类别数量: {len(classes)}")
        except Exception as e:
            print(f"数据加载失败: {e}")
            traceback.print_exc()
            return

        # --- 定义要处理的模型列表 ---
        models_to_process = args.model_names # 从参数获取模型列表
        print_section(f"准备处理模型: {', '.join(models_to_process)}")

        # --- 循环处理每个模型 ---
        for model_name in models_to_process:
            print_section(f"开始处理模型: {model_name}")
            model_run_output_dir = os.path.join(global_run_output_dir, model_name)
            os.makedirs(model_run_output_dir, exist_ok=True)
            print(f"模型 {model_name} 的输出目录: {model_run_output_dir}")

            # 初始化当前模型的变量
            current_model = None
            trained_model_path = None
            
            try:
                # --- 1. 训练模型 --- 
                print(f"\n--- 步骤 1: 训练模型 {model_name} ---")
                try:
                    # 创建模型
                    current_model = create_model(
                        model_name=model_name,
                        num_classes=len(classes),
                        pretrained=args.pretrained # 使用预训练权重开始
                    )
                    current_model = current_model.to(device)
                    model_info = get_model_info(current_model)
                    print(f"模型类型: {model_info['model_type']}")
                   
                    
                    # 训练模型
                    current_model, history = train_model(
                        model=current_model,
                        train_loader=train_loader,
                        val_loader=val_loader,
                        device=device,
                        num_epochs=args.epochs,
                        learning_rate=args.learning_rate,
                        weight_decay=args.weight_decay,
                        output_dir=model_run_output_dir # 保存到模型特定目录
                    )
                    
                    # 保存训练后的模型
                    trained_model_path = os.path.join(model_run_output_dir, "model.pth")
                    torch.save(current_model.state_dict(), trained_model_path)
                    print(f"训练后的模型 {model_name} 已保存到: {trained_model_path}")

                except Exception as e:
                    print(f"模型 {model_name} 训练失败: {e}")
                    traceback.print_exc()
                    # 训练失败，跳过此模型的后续步骤
                    continue 
                
                # 如果没有成功训练模型，则跳过后续
                if current_model is None or trained_model_path is None:
                    print(f"模型 {model_name} 未能成功训练，跳过后续步骤。")
                    continue

                # 保存原始/训练后模型的副本，用于后续优化
                original_model_for_optim = deepcopy(current_model)
                
                # --- 2. 评估训练后的模型 --- 
                print(f"\n--- 步骤 2: 评估训练后的模型 {model_name} ---")
                try:
                    evaluate_model(
                        model=current_model, 
                        dataloader=test_loader, 
                        device=device, 
                        classes=classes,
                        save_dir=model_run_output_dir, # 结果保存在模型特定目录
                        model_name=model_name
                    )
                except Exception as e:
                    print(f"模型 {model_name} 评估失败: {e}")
                    traceback.print_exc()
                    # 评估失败不影响后续优化步骤

                # --- 3. 模型优化 (剪枝、蒸馏) --- 
                # 剪枝
                if args.optimize_mode in ['prune', 'all']:
                    print(f"\n--- 步骤 3a: 模型剪枝 {model_name} ---")
                    pruned_base_dir = os.path.join(model_run_output_dir, "pruned")
                    os.makedirs(pruned_base_dir, exist_ok=True)

                    for method in args.pruning_methods.split(','):
                        method = method.strip()
                        if not method: continue
                        for ratio_str in args.pruning_ratios.split(','):
                            try:
                                ratio = float(ratio_str.strip())
                                if not 0 < ratio < 1:
                                    print(f"跳过无效的剪枝比例: {ratio}")
                                    continue
                            except ValueError:
                                print(f"跳过无效的剪枝比例: {ratio_str}")
                                continue
                            
                            print(f"\n--- {method} 剪枝, 比例: {ratio:.2f} (模型: {model_name}) ---")
                            pruned_model_dir = os.path.join(pruned_base_dir, f"{method}_{int(ratio*100)}")
                            os.makedirs(pruned_model_dir, exist_ok=True)
                            
                            try:
                                # 使用训练后模型的副本进行剪枝
                                current_model_to_prune = deepcopy(original_model_for_optim).to(device)
                                
                                # 应用剪枝
                                if method == 'global':
                                    pruned_model_instance = prune_model_global(current_model_to_prune, ratio)
                                elif method == 'structured':
                                    pruned_model_instance = prune_model_l1_structured(current_model_to_prune, ratio)
                                elif method == 'unstructured':
                                    pruned_model_instance = prune_model_l1_unstructured(current_model_to_prune, ratio)
                                else:
                                    print(f"警告: 不支持的剪枝方法 '{method}'")
                                    continue
                                
                                sparsity = calculate_sparsity(pruned_model_instance)
                                print(f"剪枝后稀疏度: {sparsity:.4f}")
                                
                                # 微调
                                if args.fine_tune_epochs > 0:
                                    print(f"微调模型 {args.fine_tune_epochs} 轮...")
                                    pruned_model_instance = fine_tune(
                                        model=pruned_model_instance,
                                        train_loader=train_loader,
                                        val_loader=val_loader,
                                        device=device,
                                        epochs=args.fine_tune_epochs,
                                        lr=args.fine_tune_lr
                                    )
                                
                                # 评估剪枝后的模型
                                print("评估剪枝模型...")
                                evaluate_model(
                                    model=pruned_model_instance,
                                    dataloader=test_loader,
                                    device=device,
                                    classes=classes,
                                    save_dir=pruned_model_dir,
                                    model_name=f"pruned_{method}_{int(ratio*100)}"
                                )
                                
                                # 保存剪枝模型
                                pruned_model_path = os.path.join(pruned_model_dir, f"pruned_{method}_{int(ratio*100)}.pth")
                                torch.save(pruned_model_instance.state_dict(), pruned_model_path)
                                print(f"剪枝模型已保存到: {pruned_model_path}")

                            except Exception as e:
                                print(f"处理模型 {model_name} 的剪枝 {method} (ratio={ratio}) 时出错: {e}")
                                traceback.print_exc()
                
                # 知识蒸馏
                if args.optimize_mode in ['distill', 'all']:
                    print(f"\n--- 步骤 3b: 知识蒸馏 (教师: {model_name}) ---")
                    distilled_dir = os.path.join(model_run_output_dir, "distilled")
                    os.makedirs(distilled_dir, exist_ok=True)
                    
                    try:
                        # 教师模型 (使用训练后模型的副本)
                        teacher_model = deepcopy(original_model_for_optim).to(device)
                        teacher_model.eval()
                        print(f"教师模型: {model_name}")
                        
                        # 创建学生模型
                        print(f"创建学生模型: {args.student_model}...")
                        student_model = create_student_model(
                            model_type=args.student_model,
                            num_classes=len(classes),
                            pretrained=True # 通常学生模型从预训练开始
                        ).to(device)
                        
                        print(f"蒸馏参数: 温度={args.temperature}, Alpha={args.alpha}, Epochs={args.distill_epochs}")
                        
                        # 训练学生模型
                        criterion = nn.CrossEntropyLoss()
                        optimizer = optim.Adam(student_model.parameters(), lr=args.distill_lr)
                        
                        student_model = train_with_distillation(
                            teacher_model=teacher_model,
                            student_model=student_model,
                            train_loader=train_loader,
                            val_loader=val_loader,
                            device=device,
                            criterion=criterion,
                            optimizer=optimizer,
                            num_epochs=args.distill_epochs,
                            alpha=args.alpha,
                            temperature=args.temperature,
                            learning_rate=args.distill_lr # 确保传入lr
                        )
                        
                        # 评估学生模型
                        print("评估蒸馏后的学生模型...")
                        evaluate_model(
                            model=student_model,
                            dataloader=test_loader,
                            device=device,
                            classes=classes,
                            save_dir=distilled_dir,
                            model_name=f"student_{args.student_model}"
                        )
                        
                        # 保存学生模型
                        student_model_path = os.path.join(distilled_dir, f"student_{args.student_model}.pth")
                        torch.save(student_model.state_dict(), student_model_path)
                        print(f"学生模型已保存到: {student_model_path}")

                    except Exception as e:
                        print(f"模型 {model_name} 的知识蒸馏失败: {e}")
                        traceback.print_exc()

                # --- 4. 量化提示 --- 
                if args.optimize_mode in ['quantize', 'all']:
                    print(f"\n--- 步骤 4: 模型量化提示 {model_name} ---")
                    # Construct the parent directory of the model's run directory
                    parent_run_dir = os.path.dirname(model_run_output_dir)
                    print("所有模型处理完毕后，可使用以下命令对该运行目录下的所有模型进行批量静态量化:")
                    print(f"python src/quanti.py \\\\\\n    --run_directory {parent_run_dir} \\\\\\n    --data_dir {args.data_dir} \\\\\\n    --calibration_batches 10") # 使用父目录进行批量处理

            except Exception as inner_e:
                 print(f"处理模型 {model_name} 过程中发生未捕获错误: {inner_e}")
                 traceback.print_exc()
            finally:
                print_section(f"完成处理模型: {model_name}")

        print_section("所有模型处理完毕")
        print(f"全局运行输出保存在: {global_run_output_dir}")
        
    except Exception as e:
        print(f"执行过程中发生严重错误: {e}")
        traceback.print_exc()

def evaluate_optimized_model(
    model: nn.Module,
    data_loader: DataLoader,
    device: torch.device,
    classes: List[str],
    output_dir: str,
    model_name: str,
) -> Dict[str, float]:
    """
    评估优化后的模型并保存结果
    
    Args:
        model: 优化后的模型
        data_loader: 数据加载器
        device: 计算设备
        classes: 类别列表
        output_dir: 输出目录
        model_name: 模型名称
    Returns:
        评估结果字典
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 评估模型并保存结果
    results = evaluate_model(
        model=model,
        dataloader=data_loader,
        device=device,
        classes=classes,
        save_dir=output_dir,
        model_name=model_name,
    )
    
    return results


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="自动化训练、评估和优化多个遥感图像分类模型")
    
    
    parser.add_argument("--output_dir", type=str, default="outputs", 
                       help="基础输出目录")
    

    # 数据参数
    parser.add_argument("--data_dir", type=str, default="PatternNet/images", 
                       help="数据集目录")
    parser.add_argument("--batch_size", type=int, default=64, 
                       help="训练和评估的批量大小")
    
    # 模型参数
    parser.add_argument("--model_names", nargs='+', default=['resnet50', 'densenet201', 'vit_s_16', 'swin_t'],
                         help="要处理的模型名称列表 (e.g., resnet50 densenet201 swin_t)")
    
    parser.add_argument("--pretrained", action="store_true", default=True, 
                       help="使用ImageNet预训练权重开始训练 (推荐, 默认为 True)")
    
    # 训练参数
    parser.add_argument("--epochs", type=int, default=40, # 默认减少轮数以便快速测试
                       help="每个模型的训练轮数")
    parser.add_argument("--learning_rate", type=float, default=0.001, 
                       help="训练学习率")
    parser.add_argument("--weight_decay", type=float, default=1e-4, 
                       help="训练权重衰减")


    # 优化参数
    parser.add_argument("--optimize_mode", type=str, default="all", 
                       choices=["none", "prune", "distill", "quantize", "all"],
                       help="要执行的优化类型: 'none', 'prune', 'distill', 'quantize'(仅提示), 'all' (执行所有，除量化外)")
    
    # 剪枝参数
    parser.add_argument("--pruning_methods", type=str, default="global", 
                       help="剪枝方法，多选用逗号分隔 (e.g., 'global,structured')")
    parser.add_argument("--pruning_ratios", type=str, default="0.5", 
                       help="剪枝比例，多选用逗号分隔 (e.g., '0.3,0.5')")
    parser.add_argument("--fine_tune_epochs", type=int, default=0, # 默认减少轮数
                       help="剪枝后微调轮数 (0 表示不微调)")
    parser.add_argument("--fine_tune_lr", type=float, default=1e-5, 
                       help="剪枝后微调学习率")
    
    # 知识蒸馏参数
    parser.add_argument("--student_model", type=str, default="mobilenetv2",
                       choices=["resnet18", "mobilenetv2"], 
                       help="学生模型类型")
    parser.add_argument("--temperature", type=float, default=4.0, 
                       help="蒸馏温度参数")
    parser.add_argument("--alpha", type=float, default=0.7, 
                       help="蒸馏软目标权重 (hard_loss*(1-alpha) + soft_loss*alpha)")
    parser.add_argument("--distill_epochs", type=int, default=25, # 默认减少轮数 
                       help="蒸馏训练轮数")
    parser.add_argument("--distill_lr", type=float, default=0.001, 
                       help="蒸馏学习率")
    
    
    # 硬件参数
    parser.add_argument("--device", type=str, default="cuda", 
                       help="计算设备 (cuda or cpu)")
    parser.add_argument("--num_workers", type=int, default=4, 
                       help="数据加载线程数")
    
    args = parser.parse_args()
    

    
    # 运行主流程
    main(args) 