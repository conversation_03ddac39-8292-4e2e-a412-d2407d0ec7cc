"""
模型信息相关的Pydantic模型定义

包含模型管理和比较的数据模型
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

class ModelMetrics(BaseModel):
    """模型评估指标"""
    accuracy: float = Field(..., description="准确率")
    precision: float = Field(..., description="精确率")
    recall: float = Field(..., description="召回率")
    f1: float = Field(..., description="F1分数")
    parameter_count: Optional[int] = Field(None, description="参数数量")

class ModelVariantInfo(BaseModel):
    """模型变体信息"""
    path: str = Field(..., description="模型文件路径")
    size_mb: float = Field(..., description="模型大小(MB)")
    metrics: ModelMetrics = Field(..., description="评估指标")
    comparison: Optional[Dict[str, Any]] = Field(None, description="参数比较信息")

class ModelTypeInfo(BaseModel):
    """模型类型完整信息"""
    model_type: str = Field(..., description="模型类型")
    original: Optional[ModelVariantInfo] = Field(None, description="原始模型信息")
    pruned: Optional[ModelVariantInfo] = Field(None, description="剪枝模型信息")
    distilled: Optional[ModelVariantInfo] = Field(None, description="蒸馏模型信息")
    quantized: Optional[ModelVariantInfo] = Field(None, description="量化模型信息")

class ModelInfoResponse(BaseModel):
    """模型信息响应"""
    success: bool = Field(..., description="请求是否成功")
    model_info: Optional[ModelTypeInfo] = Field(None, description="模型信息")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")

class ComparisonMetric(BaseModel):
    """比较指标"""
    metric_name: str = Field(..., description="指标名称")
    original_value: str = Field(..., description="原始模型值")
    optimized_value: str = Field(..., description="优化模型值")
    relative_change: str = Field(..., description="相对变化")

class ModelComparisonData(BaseModel):
    """模型比较数据"""
    comparison_type: str = Field(..., description="比较类型(pruning/distillation/quantization)")
    model_type: str = Field(..., description="模型类型")
    original_model_name: str = Field(..., description="原始模型名称")
    optimized_model_name: str = Field(..., description="优化模型名称")
    metrics: List[ComparisonMetric] = Field(..., description="比较指标列表")

class ModelComparisonResponse(BaseModel):
    """模型比较响应"""
    success: bool = Field(..., description="请求是否成功")
    comparison_data: Optional[ModelComparisonData] = Field(None, description="比较数据")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")

class ModelTypesResponse(BaseModel):
    """模型类型列表响应"""
    success: bool = Field(..., description="请求是否成功")
    model_types: List[str] = Field(..., description="可用模型类型列表")
    total_count: int = Field(..., description="模型类型总数")
    message: Optional[str] = Field(None, description="响应消息")

class ModelSummaryInfo(BaseModel):
    """模型摘要信息"""
    model_type: str = Field(..., description="模型类型")
    variants_available: List[str] = Field(..., description="可用变体列表")
    total_variants: int = Field(..., description="变体总数")
    has_original: bool = Field(..., description="是否有原始模型")
    has_pruned: bool = Field(..., description="是否有剪枝模型")
    has_distilled: bool = Field(..., description="是否有蒸馏模型")
    has_quantized: bool = Field(..., description="是否有量化模型")

class ModelSummaryResponse(BaseModel):
    """模型摘要响应"""
    success: bool = Field(..., description="请求是否成功")
    models_summary: List[ModelSummaryInfo] = Field(..., description="模型摘要列表")
    total_model_types: int = Field(..., description="模型类型总数")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")
