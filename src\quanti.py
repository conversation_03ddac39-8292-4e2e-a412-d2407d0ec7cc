from optimization.quantization import quantize_model_static
from data.dataset import create_dataloaders
from main import evaluate_optimized_model
import torch

train_loader, val_loader, test_loader, classes = create_dataloaders(
    data_dir="PatternNet/images", batch_size=32, num_workers=4
)
print("量化开始")
quantized_model = quantize_model_static(
    calibration_loader=train_loader, num_calibration_batches=32
)
print("量化完成")


print("加载量化模型")
model = torch.jit.load("outputs/run_20250410_111418/resnet50/quantized/quantized.pt")
model.eval()
print("量化模型加载完成")

print("评估开始")
evaluate_optimized_model(
    model=model,
    data_loader=test_loader,
    device="cpu",
    classes=classes,
    output_dir="outputs/run_20250410_111418/resnet50/quantized",
    model_name="quantized",
)
print("评估完成")
