# Core ML and Data Science Dependencies
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0
Pillow>=10.0.0
opencv-python>=4.8.0

# Deep Learning and Model Management
timm>=0.9.0
tensorboard>=2.13.0
torchmetrics>=1.0.0
pytorch-lightning>=2.0.0

# Progress and Utilities
tqdm>=4.65.0
rich>=13.0.0
click>=8.1.0

# Web Framework and API
fastapi>=0.100.0
uvicorn[standard]>=0.23.0
python-multipart>=0.0.6
jinja2>=3.1.0
aiofiles>=23.0.0

# UI and Visualization
gradio>=3.40.0
plotly>=5.15.0
streamlit>=1.25.0

# Data Processing and Storage
h5py>=3.9.0
scipy>=1.11.0
imageio>=2.31.0
albumentations>=1.3.0

# Configuration and Environment
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
omegaconf>=2.3.0

# Monitoring and Logging
loguru>=0.7.0
wandb>=0.15.0
mlflow>=2.5.0

# HTTP and Networking
httpx>=0.24.0
requests>=2.31.0
websockets>=11.0.0

# Development and Testing (Optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0

# Security
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4

# Performance
psutil>=5.9.0
memory-profiler>=0.61.0