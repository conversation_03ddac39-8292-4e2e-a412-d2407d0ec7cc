import os
import argparse
import time
from pathlib import Path
from typing import Tuple, List, Dict
import datetime

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm import tqdm

from data.dataset import create_dataloaders
from models.base_model import create_model
from utils.metrics import compute_accuracy, evaluate_model
from utils.visualization import plot_training_curves, create_tensorboard_writer, log_metrics_to_tensorboard


def train_epoch(
    model: nn.Module, 
    dataloader: DataLoader, 
    criterion: nn.Module, 
    optimizer: optim.Optimizer, 
    device: torch.device
) -> Tuple[float, float]:
    """
    训练一个epoch
    
    Args:
        model: 模型
        dataloader: 数据加载器
        criterion: 损失函数
        optimizer: 优化器
        device: 计算设备
        
    Returns:
        (平均损失, 平均准确率)
    """
    model.train()
    running_loss = 0.0
    running_corrects = 0
    processed_size = 0
    
    for inputs, labels in tqdm(dataloader, desc="Training"):
        inputs, labels = inputs.to(device), labels.to(device)
        
        # 清零梯度
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(inputs)
        loss = criterion(outputs, labels)
        
        # 反向传播和优化
        loss.backward()
        optimizer.step()
        
        # 统计
        running_loss += loss.item() * inputs.size(0)
        _, preds = torch.max(outputs, 1)
        running_corrects += torch.sum(preds == labels.data).item()
        processed_size += inputs.size(0)
    
    epoch_loss = running_loss / processed_size
    epoch_acc = running_corrects / processed_size
    
    return epoch_loss, epoch_acc


def validate(
    model: nn.Module, 
    dataloader: DataLoader, 
    criterion: nn.Module, 
    device: torch.device
) -> Tuple[float, float]:
    """
    在验证集上评估模型
    
    Args:
        model: 模型
        dataloader: 数据加载器
        criterion: 损失函数
        device: 计算设备
        
    Returns:
        (平均损失, 平均准确率)
    """
    model.eval()
    running_loss = 0.0
    running_corrects = 0
    processed_size = 0
    
    with torch.no_grad():
        for inputs, labels in tqdm(dataloader, desc="Validating"):
            inputs, labels = inputs.to(device), labels.to(device)
            
            # 前向传播
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # 统计
            running_loss += loss.item() * inputs.size(0)
            _, preds = torch.max(outputs, 1)
            running_corrects += torch.sum(preds == labels.data).item()
            processed_size += inputs.size(0)
    
    epoch_loss = running_loss / processed_size
    epoch_acc = running_corrects / processed_size
    
    return epoch_loss, epoch_acc


def train_model(
    model: nn.Module,
    train_loader: DataLoader,
    val_loader: DataLoader,
    criterion: nn.Module = None,
    optimizer: optim.Optimizer = None,
    scheduler: torch.optim.lr_scheduler._LRScheduler = None,
    device: torch.device = None,
    num_epochs: int = 20,
    checkpoint_dir: str = None,
    tensorboard_dir: str = None,
    learning_rate: float = 0.001,
    weight_decay: float = 1e-4,
    output_dir: str = None
) -> Tuple[nn.Module, Dict]:
    """
    训练模型
    
    Args:
        model: 待训练的模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        criterion: 损失函数 (可选)
        optimizer: 优化器 (可选)
        scheduler: 学习率调度器 (可选)
        device: 计算设备 (可选)
        num_epochs: 训练轮数
        checkpoint_dir: 模型保存目录 (可选)
        tensorboard_dir: TensorBoard日志目录 (可选)
        learning_rate: 学习率 (当optimizer为None时使用)
        weight_decay: 权重衰减 (当optimizer为None时使用)
        output_dir: 输出目录 (当checkpoint_dir和tensorboard_dir为None时使用)
        
    Returns:
        (训练好的模型, 训练历史)
    """
    # 确保设备已设置
    if device is None:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(device)
    
    # 处理简化调用的情况
    if output_dir is not None:
        if checkpoint_dir is None:
            checkpoint_dir = os.path.join(output_dir, "checkpoints")
        if tensorboard_dir is None:
            tensorboard_dir = os.path.join(output_dir, "tensorboard")
    else:
        # 确保至少有一个输出路径
        if checkpoint_dir is None:
            checkpoint_dir = "checkpoints"
        if tensorboard_dir is None:
            tensorboard_dir = "tensorboard"
    
    # 创建保存目录
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(tensorboard_dir, exist_ok=True)
    
    # 如果未提供criterion，使用交叉熵损失
    if criterion is None:
        criterion = nn.CrossEntropyLoss()
    
    # 如果未提供optimizer，创建Adam优化器
    if optimizer is None:
        optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    
    # 如果未提供scheduler，创建StepLR
    if scheduler is None:
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
    
    # 创建TensorBoard写入器
    try:
        writer = create_tensorboard_writer(tensorboard_dir)
    except Exception as e:
        print(f"创建TensorBoard写入器失败: {e}")
        writer = None
    
    # 记录训练过程
    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []
    
    # 记录最佳模型
    best_acc = 0.0
    best_model_path = ""
    
    # 训练循环
    for epoch in range(num_epochs):
        print(f"Epoch {epoch+1}/{num_epochs}")
        print('-' * 10)
        
        # 训练阶段
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)
        print(f"Train Loss: {train_loss:.4f} Acc: {train_acc:.4f}")
        
        # 验证阶段
        val_loss, val_acc = validate(model, val_loader, criterion, device)
        print(f"Val Loss: {val_loss:.4f} Acc: {val_acc:.4f}")
        
        # 学习率调整
        if scheduler:
            scheduler.step()
            if writer:
                try:
                    writer.add_scalar('learning_rate', scheduler.get_last_lr()[0], epoch)
                except:
                    pass
        
        # 记录指标
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        train_accs.append(train_acc)
        val_accs.append(val_acc)
        
        # 保存到TensorBoard
        if writer:
            try:
                writer.add_scalar('loss/train', train_loss, epoch)
                writer.add_scalar('loss/val', val_loss, epoch)
                writer.add_scalar('accuracy/train', train_acc, epoch)
                writer.add_scalar('accuracy/val', val_acc, epoch)
            except Exception as e:
                print(f"写入TensorBoard时出错: {e}")
        
        # 保存最佳模型
        if val_acc > best_acc:
            best_acc = val_acc
            best_model_path = os.path.join(checkpoint_dir, "best_model.pth")
            try:
                torch.save(model.state_dict(), best_model_path)
                print(f"保存最佳模型，准确率: {best_acc:.4f}")
            except Exception as e:
                print(f"保存最佳模型时出错: {e}")
                best_model_path = ""
        
        print()
    
    # 绘制训练曲线
    try:
        plot_path = os.path.join(checkpoint_dir, "training_curves.png")
        plot_training_curves(train_losses, val_losses, train_accs, val_accs, plot_path)
        print(f"训练曲线已保存到: {plot_path}")
    except Exception as e:
        print(f"绘制训练曲线时出错: {e}")
    
    # 加载最佳模型
    if best_model_path and os.path.exists(best_model_path):
        try:
            model.load_state_dict(torch.load(best_model_path, map_location=device))
            print(f"已加载最佳模型: {best_model_path}")
        except Exception as e:
            print(f"加载最佳模型时出错: {e}")
    else:
        print("未找到最佳模型，使用当前模型")
    
    # 关闭TensorBoard写入器
    if writer:
        writer.close()
    
    # 创建并返回历史记录
    history = {
        'train_loss': train_losses,
        'val_loss': val_losses,
        'train_acc': train_accs,
        'val_acc': val_accs,
        'best_acc': best_acc,
        'best_model_path': best_model_path
    }
    
    # 添加绘图方法到历史记录
    def plot():
        try:
            import matplotlib.pyplot as plt
            plt.figure(figsize=(12, 4))
            
            plt.subplot(1, 2, 1)
            plt.plot(train_losses, label='训练')
            plt.plot(val_losses, label='验证')
            plt.title('损失曲线')
            plt.xlabel('轮次')
            plt.ylabel('损失')
            plt.legend()
            
            plt.subplot(1, 2, 2)
            plt.plot(train_accs, label='训练')
            plt.plot(val_accs, label='验证')
            plt.title('准确率曲线')
            plt.xlabel('轮次')
            plt.ylabel('准确率')
            plt.legend()
            
            plt.tight_layout()
        except Exception as e:
            print(f"绘制历史曲线时出错: {e}")
    
    history['plot'] = plot
    
    return model, history


def main(args):
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = args.output_dir
    if not args.no_timestamp:
        output_dir = os.path.join(output_dir, f"run_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)

    # 保存参数
    args_path = os.path.join(output_dir, "args.txt")
    with open(args_path, "w") as f:
        for arg, value in vars(args).items():
            f.write(f"{arg}: {value}\n")

    # 创建数据加载器
    train_loader, val_loader, _, classes = create_dataloaders(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers
    )

    # 创建模型
    print(f"创建模型: {args.model_name}...")
    model = create_model(
        model_name=args.model_name,
        num_classes=len(classes),
        pretrained=args.pretrained
    )
    model = model.to(device)
    
    # 如果提供了模型路径，加载权重
    if args.resume:
        if os.path.exists(args.resume):
            print(f"加载模型权重: {args.resume}")
            model.load_state_dict(torch.load(args.resume, map_location=device))
        else:
            print(f"警告: 模型权重文件 {args.resume} 不存在，将从头开始训练或使用预训练权重。")
    elif not args.pretrained:
         print("警告: 未提供模型权重文件，且未指定使用预训练权重，将随机初始化模型参数。")


    # 训练模型
    model, history = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        device=device,
        num_epochs=args.epochs,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        output_dir=output_dir
    )

    # 绘制并保存训练曲线
    plot_training_curves(history, os.path.join(output_dir, "training_curves.png"))
    
    # 保存最终模型
    final_model_path = os.path.join(output_dir, "model.pth")
    torch.save(model.state_dict(), final_model_path)
    print(f"训练完成。最终模型已保存到 {final_model_path}")
    
    # 保存类别信息
    class_file = os.path.join(output_dir, "classes.json")
    try:
        import json
        with open(class_file, 'w') as f:
            json.dump(classes, f, indent=4)
        print(f"类别信息已保存到 {class_file}")
    except Exception as e:
        print(f"保存类别文件时出错: {e}")
    
    # 可选：在训练结束后进行评估
    if args.evaluate_after_train:
        print("\n开始评估训练后的模型...")
        evaluate_model(
            model=model, 
            dataloader=val_loader, # 或者创建一个独立的测试加载器
            device=device, 
            classes=classes,
            save_dir=output_dir, 
            model_name=args.model_name
        )
    
    return final_model_path


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="训练模型")
    
    # 数据参数
    parser.add_argument("--data_dir", type=str, default="PatternNet/images", help="数据集目录")
    parser.add_argument("--batch_size", type=int, default=32, help="批量大小")
    
    # 模型参数
    parser.add_argument("--model_name", type=str, default="resnet50", help="模型名称 (e.g., resnet50, densenet201, vit_s_16, swin_t)")
    parser.add_argument("--pretrained", action="store_true", help="使用ImageNet预训练权重开始训练")
    parser.add_argument("--resume", type=str, default=None, help="从指定的模型权重文件恢复训练")
    
    # 训练参数
    parser.add_argument("--epochs", type=int, default=20, help="训练轮数")
    parser.add_argument("--learning_rate", type=float, default=0.001, help="学习率")
    parser.add_argument("--weight_decay", type=float, default=1e-4, help="权重衰减")
    
    # 硬件参数
    parser.add_argument("--device", type=str, default="cuda", help="设备")
    parser.add_argument("--num_workers", type=int, default=4, help="数据加载线程数")
    
    # 输出参数
    parser.add_argument("--output_dir", type=str, default="outputs", help="输出目录")
    parser.add_argument("--no_timestamp", action="store_true", help="不在输出目录中添加时间戳")
    parser.add_argument("--evaluate_after_train", action="store_true", help="训练结束后在验证集上评估模型")
    
    args = parser.parse_args()
    
    main(args) 