import torch
import torch.nn as nn
from torchvision import models
from torchvision.models.resnet import ResNet50_Weights
from torchvision.models import DenseNet201_Weights
import torchvision.models.quantization as models_quant
from typing import Optional, Dict, Any

# 尝试导入timm
try:
    import timm
except ImportError:
    timm = None
    print("警告: timm 库未安装。将无法使用 ViT 和 Swin Transformer 模型。请使用 'pip install timm' 安装。")


class ResNet50Model(nn.Module):
    """使用ResNet50作为基础模型的图像分类器"""
    
    def __init__(self, num_classes: int, pretrained: bool = True):
        """
        初始化ResNet50模型
        
        Args:
            num_classes: 分类类别数量
            pretrained: 是否使用预训练权重
        """
        super(ResNet50Model, self).__init__()
        
        # 加载预训练的ResNet50模型，使用新的权重枚举方式
        if pretrained:
            weights = ResNet50_Weights.IMAGENET1K_V1
        else:
            weights = None
            
        self.model = models.resnet50(weights=weights)
        
        # 修改最后的全连接层以适应我们的类别数
        in_features = self.model.fc.in_features
        self.model.fc = nn.Linear(in_features, num_classes)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入图像张量，形状为(B, C, H, W)
            
        Returns:
            类别预测的logits，形状为(B, num_classes)
        """
        return self.model(x)
    
    def save(self, path: str):
        """
        保存模型权重
        
        Args:
            path: 保存路径
        """
        torch.save(self.state_dict(), path)
    
    def load(self, path: str, map_location: Optional[str] = None):
        """
        加载模型权重
        
        Args:
            path: 权重文件路径
            map_location: 加载设备
        """
        self.load_state_dict(torch.load(path, map_location=map_location))


class DenseNet201Model(nn.Module):
    """使用DenseNet201作为基础模型的图像分类器"""
    
    def __init__(self, num_classes: int, pretrained: bool = True):
        """
        初始化DenseNet201模型
        
        Args:
            num_classes: 分类类别数量
            pretrained: 是否使用预训练权重
        """
        super(DenseNet201Model, self).__init__()
        
        # 加载预训练的DenseNet201模型
        if pretrained:
            weights = DenseNet201_Weights.IMAGENET1K_V1
        else:
            weights = None
            
        self.model = models.densenet201(weights=weights)
        
        # 修改最后的分类器层以适应我们的类别数
        in_features = self.model.classifier.in_features
        self.model.classifier = nn.Linear(in_features, num_classes)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.model(x)
    
    def save(self, path: str):
        torch.save(self.state_dict(), path)
    
    def load(self, path: str, map_location: Optional[str] = None):
        self.load_state_dict(torch.load(path, map_location=map_location))


class ViTModel(nn.Module):
    """使用timm库中的Vision Transformer模型"""
    
    def __init__(self, model_name: str, num_classes: int, pretrained: bool = True):
        """
        初始化Vision Transformer模型
        
        Args:
            model_name: timm中的模型名称 (e.g., 'vit_small_patch16_224')
            num_classes: 分类类别数量
            pretrained: 是否使用预训练权重
        """
        super(ViTModel, self).__init__()
        if timm is None:
            raise ImportError("timm库未安装，无法创建ViT模型。")
            
        self.model = timm.create_model(
            model_name, 
            pretrained=pretrained, 
            num_classes=num_classes
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.model(x)
    
    def save(self, path: str):
        torch.save(self.state_dict(), path)
    
    def load(self, path: str, map_location: Optional[str] = None):
        self.load_state_dict(torch.load(path, map_location=map_location))


class SwinModel(nn.Module):
    """使用timm库中的Swin Transformer模型"""
    
    def __init__(self, model_name: str, num_classes: int, pretrained: bool = True):
        """
        初始化Swin Transformer模型
        
        Args:
            model_name: timm中的模型名称 (e.g., 'swin_tiny_patch4_window7_224')
            num_classes: 分类类别数量
            pretrained: 是否使用预训练权重
        """
        super(SwinModel, self).__init__()
        if timm is None:
            raise ImportError("timm库未安装，无法创建Swin Transformer模型。")
            
        self.model = timm.create_model(
            model_name, 
            pretrained=pretrained, 
            num_classes=num_classes
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.model(x)
    
    def save(self, path: str):
        torch.save(self.state_dict(), path)
    
    def load(self, path: str, map_location: Optional[str] = None):
        self.load_state_dict(torch.load(path, map_location=map_location))


def create_model(
    model_name: str = "resnet50", # 默认模型改为resnet50
    num_classes: int = 38, 
    pretrained: bool = True,
    **kwargs
) -> nn.Module:
    """
    创建指定名称的模型实例
    
    Args:
        model_name: 模型名称 ('resnet50', 'densenet201', 'vit_s_16', 'swin_t')
        num_classes: 分类类别数量
        pretrained: 是否使用预训练权重
        kwargs: 其他参数
        
    Returns:
        模型实例
    """
    model_name = model_name.lower()
    
    if model_name == "resnet50":
        return ResNet50Model(num_classes=num_classes, pretrained=pretrained)
    elif model_name == "densenet201":
        return DenseNet201Model(num_classes=num_classes, pretrained=pretrained)
    elif model_name == "vit_s_16":
        if timm is None:
            raise ImportError("timm库未安装，无法创建ViT模型。")
        return ViTModel("vit_small_patch16_224", num_classes=num_classes, pretrained=pretrained)
    elif model_name == "swin_t":
        if timm is None:
            raise ImportError("timm库未安装，无法创建Swin Transformer模型。")
        return SwinModel("swin_tiny_patch4_window7_224", num_classes=num_classes, pretrained=pretrained)
    else:
        raise ValueError(f"未知的模型名称: {model_name}")


def get_model_info(model: nn.Module) -> Dict[str, Any]:
    """
    获取模型信息
    
    Args:
        model: PyTorch模型
        
    Returns:
        包含模型信息的字典
    """
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    # 尝试获取模型类型 (可能需要根据模型结构调整)
    model_type = "Unknown"
    if hasattr(model, 'model'): # 适用于ResNet, DenseNet等结构
        model_type = type(model.model).__name__
        if hasattr(model.model, 'blocks'): # 可能是 ViT
            model_type = "ViT"
        elif hasattr(model.model, 'layers'): # 可能是 Swin
            model_type = "SwinTransformer"
    elif isinstance(model, ResNet50Model): # 显式检查包装类
        model_type = "ResNet50"
    elif isinstance(model, DenseNet201Model):
        model_type = "DenseNet201"
    elif isinstance(model, ViTModel):
        model_type = "ViT"
    elif isinstance(model, SwinModel):
        model_type = "SwinTransformer"
    
    return {
        "total_parameters": total_params,
        "trainable_parameters": trainable_params,
        "model_type": model_type,
    }


# --- Float Model Classes (Primarily for Quantization Compatibility) ---
# 这些类旨在提供一个统一的接口，特别是对于那些有特定量化对应结构的模型 (如 ResNet)，
# 或者只是为了在量化流程中明确表示加载的是浮点模型。

class ResNet50FloatModel(nn.Module):
    """Float model wrapper for ResNet50, using models_quant base."""
    def __init__(self, num_classes: int, pretrained: bool = True):
        super().__init__()
        if pretrained:
            weights = ResNet50_Weights.IMAGENET1K_V1
        else:
            weights = None
        # 使用 models_quant.resnet50 但关闭量化，作为浮点模型的基础
        # 确保结构与稍后要加载到的 QuantizableResNet 尽可能一致
        self.model = models_quant.resnet50(weights=weights, quantize=False)

        # Replace the classifier head
        in_features = self.model.fc.in_features
        self.model.fc = nn.Linear(in_features, num_classes)

    def forward(self, x):
        return self.model(x)

    def load_float_weights(self, path: str, map_location: Optional[str] = None):
        self.load_state_dict(torch.load(path, map_location=map_location))

    def save(self, path: str):
        torch.save(self.state_dict(), path)




def create_float_model(num_classes: int, pretrained: bool = True) -> nn.Module:
        return ResNet50FloatModel(num_classes=num_classes, pretrained=pretrained)


if __name__ == "__main__":
    # 测试代码
    num_classes=38
    
    for model_name in ["resnet50", "densenet201", "vit_s_16", "swin_t"]:
        print(f"\n--- 测试模型: {model_name} ---")
        try:
            model = create_model(model_name=model_name, num_classes=num_classes)
            
            # 打印模型结构 (只打印顶层信息，避免过长)
            print(f"模型类型: {type(model).__name__}")
            # print(model) 
            
            # 获取模型信息
            model_info = get_model_info(model)
            print(f"解析的模型类型: {model_info['model_type']}")
            print(f"总参数数量: {model_info['total_parameters']:,}")
            print(f"可训练参数数量: {model_info['trainable_parameters']:,}")
            
            # 测试前向传播
            x = torch.randn(2, 3, 224, 224)  # 批次大小为2的示例输入
            out = model(x)
            print(f"输出形状: {out.shape}")  # 应该是[2, 38]
        
        except ImportError as e:
            print(f"无法创建模型 {model_name}: {e}")
        except ValueError as e:
             print(f"创建模型 {model_name} 时出错: {e}")
        except Exception as e:
            print(f"测试模型 {model_name} 时发生未知错误: {e}") 