# =============================================================================
# Docker ignore file for Remote Sensing Image Classification System
# Excludes unnecessary files from Docker build context
# =============================================================================

# Legacy exclusions
dependencies/
PatternNet.zip

# Git and version control
.git/
.gitignore
.gitattributes
.github/

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
*.pyc
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Testing and coverage
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover

# Type checking
.mypy_cache/
.dmypy.json
dmypy.json

# Documentation
docs/_build/
*.md
!README.md

# Logs and databases
*.log
*.db
*.sqlite
*.sqlite3

# Large data files and models (exclude actual files but keep structure)
old_dataset/**/*.jpg
old_dataset/**/*.jpeg
old_dataset/**/*.png
old_dataset/**/*.bmp
old_dataset/**/*.tiff
old_dataset/**/*.webp

new_dataset/**/*.jpg
new_dataset/**/*.jpeg
new_dataset/**/*.png
new_dataset/**/*.bmp
new_dataset/**/*.tiff
new_dataset/**/*.webp

combined_dataset/**/*.jpg
combined_dataset/**/*.jpeg
combined_dataset/**/*.png
combined_dataset/**/*.bmp
combined_dataset/**/*.tiff
combined_dataset/**/*.webp

# Temporary image uploads
imgs/
*.jpg
*.jpeg
*.png
*.bmp
*.tiff
*.webp

# Model checkpoints (too large for Docker build)
outputs/checkpoints/**/*.pth
outputs/checkpoints/**/*.pt
outputs/checkpoints/**/*.ckpt
outputs/checkpoints/**/*.pkl
outputs/checkpoints/**/*.h5

# Keep directory structure but exclude large files
!old_dataset/
!new_dataset/
!combined_dataset/
!outputs/
!outputs/checkpoints/

# Monitoring and experiment tracking
wandb/
mlruns/
.mlflow/
tensorboard_logs/
experiments/
runs/

# Temporary files
tmp/
temp/
.tmp/
*.tmp
*.temp

# Docker files
Dockerfile*
docker-compose*.yml

# Environment files
.env*
!.env.example
config.local.yml
secrets.yml

# Backup and archive files
*.bak
*.backup
*.old
*.orig
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# OS generated files
.DS_Store
Thumbs.db
Desktop.ini
$RECYCLE.BIN/
.Trash-*

# Development files
local/
dev/
development/
tests/fixtures/
tests/data/
test_data/