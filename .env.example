# Environment Configuration Template
# Copy this file to .env and update the values as needed

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application environment (development/production/testing)
ENVIRONMENT=development

# Application info
APP_NAME="Remote Sensing Image Classification System"
APP_VERSION="0.1.0"
APP_DESCRIPTION="Remote Sensing Image Classification with Model Optimization"

# =============================================================================
# WEB SERVER SETTINGS
# =============================================================================

# Server configuration
HOST=0.0.0.0
PORT=8000
DEBUG=true
RELOAD=true

# Security settings
SECRET_KEY=your-secret-key-change-in-production-please-use-a-strong-random-key
ALLOWED_HOSTS=*

# File upload settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
STATIC_DIR=static

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# MongoDB configuration (primary database)
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=remote_sensing_db

# SQLite configuration (fallback database)
SQLITE_PATH=data/app.db

# =============================================================================
# MODEL SETTINGS
# =============================================================================

# Model directories and files
MODELS_DIR=outputs/checkpoints
NUM_CLASSES=21
IMAGE_SIZE=224
BATCH_SIZE=32

# Model optimization features
ENABLE_PRUNING=true
ENABLE_QUANTIZATION=true
ENABLE_DISTILLATION=true

# =============================================================================
# DATA SETTINGS
# =============================================================================

# Data directories
DATA_DIR=data
DATASET_DIR=data/UC_Merced

# Data splits
TRAIN_SPLIT=0.8
VAL_SPLIT=0.1
TEST_SPLIT=0.1

# Data processing
ENABLE_AUGMENTATION=true

# =============================================================================
# LOGGING SETTINGS
# =============================================================================

# Log configuration
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_FILE=app.log
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# Log format (using Loguru format)
LOG_FORMAT="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================

# Weights & Biases (optional)
# WANDB_PROJECT=remote-sensing-classification
# WANDB_ENTITY=your-wandb-entity

# MLflow (optional)
# MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_EXPERIMENT_NAME=remote_sensing_classification

# TensorBoard
TENSORBOARD_LOG_DIR=logs/tensorboard

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
ENABLE_GRADIO=true
ENABLE_WEB_UI=true
ENABLE_API=true
ENABLE_MONITORING=true

# =============================================================================
# EXTERNAL SERVICES (Optional)
# =============================================================================

# Redis (for caching, if used)
# REDIS_URL=redis://localhost:6379

# Email settings (for notifications, if used)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_TLS=true

# Cloud storage (if used)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=us-east-1
# S3_BUCKET=your-s3-bucket

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development tools
PYTEST_ARGS="-v --tb=short"
BLACK_LINE_LENGTH=88
MYPY_STRICT=true

# Jupyter settings
JUPYTER_PORT=8888
JUPYTER_TOKEN=your-jupyter-token

# =============================================================================
# PRODUCTION SETTINGS (uncomment for production)
# =============================================================================

# Production overrides
# ENVIRONMENT=production
# DEBUG=false
# RELOAD=false
# LOG_LEVEL=WARNING

# Production security
# SECRET_KEY=your-very-secure-production-secret-key-here
# ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Production database
# MONGODB_URL=***************************************************************************

# Production monitoring
# WANDB_PROJECT=remote-sensing-production
# MLFLOW_TRACKING_URI=https://your-mlflow-server.com

# =============================================================================
# DOCKER SETTINGS (for containerized deployment)
# =============================================================================

# Docker-specific settings
# DOCKER_HOST=0.0.0.0
# DOCKER_PORT=8000
# DOCKER_WORKERS=4

# Container resource limits
# MEMORY_LIMIT=2g
# CPU_LIMIT=2

# =============================================================================
# TESTING SETTINGS
# =============================================================================

# Test database (separate from development)
# TEST_MONGODB_URL=mongodb://localhost:27017
# TEST_MONGODB_DATABASE=remote_sensing_test_db
# TEST_SQLITE_PATH=data/test.db

# Test data
# TEST_DATA_DIR=tests/data
# TEST_MODELS_DIR=tests/models

# =============================================================================
# NOTES
# =============================================================================

# 1. Copy this file to .env in the project root
# 2. Update the values according to your environment
# 3. Never commit .env files to version control
# 4. Use strong, unique values for SECRET_KEY in production
# 5. Configure proper database credentials for production
# 6. Set up monitoring services (Weights & Biases, MLflow) as needed
# 7. Adjust resource limits based on your hardware capabilities
# 8. Enable/disable features based on your requirements

# For more information, see the documentation:
# - Configuration: docs/configuration.md
# - Deployment: docs/deployment.md
# - Development: docs/development.md
