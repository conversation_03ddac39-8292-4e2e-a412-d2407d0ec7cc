#!/usr/bin/env python3
"""
快速WebSocket测试脚本

启动服务器并测试WebSocket连接，一键验证修复效果
"""

import subprocess
import asyncio
import time
import sys
import os
import signal
from test_websocket import test_websocket_connection

def start_server():
    """启动Web应用服务器"""
    print("🚀 启动Web应用服务器...")
    
    # 检查是否在正确的目录
    if not os.path.exists("src/web_app.py"):
        print("❌ 错误: 未找到 src/web_app.py")
        print("请在项目根目录运行此脚本")
        return None
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, "src/web_app.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("⏳ 等待服务器启动...")
        time.sleep(5)  # 等待服务器启动
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 服务器启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print("❌ 服务器启动失败")
            print("STDOUT:", stdout)
            print("STDERR:", stderr)
            return None
            
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")
        return None

def stop_server(process):
    """停止服务器"""
    if process and process.poll() is None:
        print("🛑 停止服务器...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        print("✅ 服务器已停止")

async def run_websocket_test():
    """运行WebSocket测试"""
    print("\n🔗 开始WebSocket连接测试...")
    print("=" * 40)
    
    success = await test_websocket_connection()
    
    if success:
        print("\n🎉 WebSocket测试通过！")
        print("✅ 403错误已修复")
        print("✅ WebSocket连接正常工作")
        return True
    else:
        print("\n❌ WebSocket测试失败")
        return False

def check_server_running():
    """检查服务器是否已在运行"""
    try:
        import requests
        response = requests.get("http://localhost:8000/api/health", timeout=3)
        if response.status_code == 200:
            print("ℹ️  检测到服务器已在运行")
            return True
    except:
        pass
    return False

def main():
    """主函数"""
    print("WebSocket快速测试工具")
    print("=" * 50)
    
    server_process = None
    server_was_running = check_server_running()
    
    try:
        if not server_was_running:
            # 启动服务器
            server_process = start_server()
            if not server_process:
                print("❌ 无法启动服务器，测试终止")
                return False
        else:
            print("✅ 使用已运行的服务器进行测试")
        
        # 运行WebSocket测试
        success = asyncio.run(run_websocket_test())
        
        if success:
            print("\n📋 测试总结:")
            print("✅ WebSocket 403错误已解决")
            print("✅ 实时连接功能正常")
            print("✅ 消息收发正常")
            print("✅ 心跳机制工作正常")
            
            print("\n🌐 你可以访问以下地址:")
            print("   - 主页: http://localhost:8000")
            print("   - API文档: http://localhost:8000/docs")
            print("   - 健康检查: http://localhost:8000/api/health")
            
            if not server_was_running:
                print("\n⚠️  服务器将继续运行，按 Ctrl+C 停止")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n👋 用户请求停止服务器")
            
            return True
        else:
            print("\n❌ 测试失败，请检查以下项目:")
            print("   1. 服务器是否正常启动")
            print("   2. 端口8000是否被占用")
            print("   3. 防火墙设置")
            print("   4. WebSocket依赖是否安装")
            return False
            
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        return False
    finally:
        if server_process and not server_was_running:
            stop_server(server_process)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"💥 程序异常退出: {e}")
        sys.exit(1)
