"""
自适应微调API路由

提供自适应微调监控控制和状态查询的RESTful API接口
"""

from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse

from ..models.adaptive import (
    AdaptiveStatusResponse, AdaptiveControlResponse, 
    DistributionCheckRequest, DistributionCheckResponse,
    ManualTuneRequest, ManualTuneResponse,
    ThresholdUpdateRequest, ThresholdUpdateResponse,
    AdaptiveConfigResponse
)
from ..services.adaptive_service import adaptive_service

router = APIRouter()

@router.get("/status", response_model=AdaptiveStatusResponse)
async def get_adaptive_status():
    """
    获取自适应微调状态
    """
    try:
        status_info = adaptive_service.get_monitoring_status()
        
        return AdaptiveStatusResponse(
            success=True,
            monitoring_active=status_info["monitoring_active"],
            fine_tuning_running=status_info["fine_tuning_running"],
            fine_tuning_message=status_info["fine_tuning_message"],
            last_check_time=status_info["last_check_time"],
            distribution_threshold=status_info["distribution_threshold"],
            message="成功获取自适应微调状态"
        )
        
    except Exception as e:
        return AdaptiveStatusResponse(
            success=False,
            monitoring_active=False,
            fine_tuning_running=False,
            fine_tuning_message="获取状态失败",
            distribution_threshold=0.1,
            error=f"获取状态失败: {str(e)}"
        )

@router.post("/start", response_model=AdaptiveControlResponse)
async def start_adaptive_monitoring():
    """
    启动自适应监控
    """
    try:
        result = adaptive_service.start_monitoring()
        
        return AdaptiveControlResponse(
            success=True,
            message=result
        )
        
    except Exception as e:
        return AdaptiveControlResponse(
            success=False,
            message="启动监控失败",
            error=str(e)
        )

@router.post("/stop", response_model=AdaptiveControlResponse)
async def stop_adaptive_monitoring():
    """
    停止自适应监控
    """
    try:
        result = adaptive_service.stop_monitoring()
        
        return AdaptiveControlResponse(
            success=True,
            message=result
        )
        
    except Exception as e:
        return AdaptiveControlResponse(
            success=False,
            message="停止监控失败",
            error=str(e)
        )

@router.post("/manual-tune", response_model=ManualTuneResponse)
async def manual_fine_tune(request: ManualTuneRequest = ManualTuneRequest()):
    """
    手动触发微调
    
    Args:
        request: 微调请求参数
    """
    try:
        result = adaptive_service.manual_tune(
            model_type=request.model_type,
            epochs=request.epochs,
            learning_rate=request.learning_rate
        )
        
        return ManualTuneResponse(
            success=True,
            message=result
        )
        
    except Exception as e:
        return ManualTuneResponse(
            success=False,
            message="手动微调失败",
            error=str(e)
        )

@router.post("/check-distribution", response_model=DistributionCheckResponse)
async def check_data_distribution(request: DistributionCheckRequest = DistributionCheckRequest()):
    """
    检查数据分布变化
    
    Args:
        request: 分布检查请求参数
    """
    try:
        needs_fine_tuning, diff_score, threshold, old_count, new_count, message = adaptive_service.check_distribution(
            threshold=request.threshold
        )
        
        return DistributionCheckResponse(
            success=True,
            needs_fine_tuning=needs_fine_tuning,
            difference_score=diff_score,
            threshold=threshold,
            old_features_count=old_count,
            new_features_count=new_count,
            message=message
        )
        
    except Exception as e:
        return DistributionCheckResponse(
            success=False,
            needs_fine_tuning=False,
            difference_score=0.0,
            threshold=0.1,
            message="检查分布失败",
            error=str(e)
        )

@router.put("/threshold", response_model=ThresholdUpdateResponse)
async def update_threshold(request: ThresholdUpdateRequest):
    """
    更新分布差异阈值
    
    Args:
        request: 阈值更新请求
    """
    try:
        success, old_threshold, new_threshold, message = adaptive_service.update_threshold(
            request.threshold
        )
        
        return ThresholdUpdateResponse(
            success=success,
            old_threshold=old_threshold,
            new_threshold=new_threshold,
            message=message
        )
        
    except Exception as e:
        return ThresholdUpdateResponse(
            success=False,
            old_threshold=0.1,
            new_threshold=request.threshold,
            message="更新阈值失败",
            error=str(e)
        )

@router.get("/config", response_model=AdaptiveConfigResponse)
async def get_adaptive_config():
    """
    获取自适应微调配置
    """
    try:
        config = adaptive_service.get_config()
        
        return AdaptiveConfigResponse(
            success=True,
            config=config,
            message="成功获取自适应微调配置"
        )
        
    except Exception as e:
        return AdaptiveConfigResponse(
            success=False,
            config={},
            error=f"获取配置失败: {str(e)}"
        )

@router.get("/fine-tuning-status")
async def get_fine_tuning_status():
    """
    获取微调状态（简化版本）
    """
    try:
        status_message = adaptive_service.get_fine_tuning_status()
        
        return JSONResponse(
            content={
                "success": True,
                "status": status_message,
                "message": "成功获取微调状态"
            }
        )
        
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "status": "获取状态失败",
                "error": str(e)
            }
        )
