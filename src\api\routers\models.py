"""
模型管理API路由

提供模型信息查询和性能比较的RESTful API接口
"""

from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse

from ..models.model_info import (
    ModelInfoResponse, ModelComparisonResponse, ModelTypesResponse, 
    ModelSummaryResponse
)
from ..services.model_service import model_service

router = APIRouter()

@router.get("/types", response_model=ModelTypesResponse)
async def get_model_types():
    """
    获取可用模型类型列表
    """
    try:
        model_types = model_service.get_model_types()
        
        return ModelTypesResponse(
            success=True,
            model_types=model_types,
            total_count=len(model_types),
            message=f"成功获取 {len(model_types)} 个模型类型"
        )
        
    except Exception as e:
        return ModelTypesResponse(
            success=False,
            model_types=[],
            total_count=0,
            message=f"获取模型类型失败: {str(e)}"
        )

@router.get("/summary", response_model=ModelSummaryResponse)
async def get_models_summary():
    """
    获取所有模型的摘要信息
    """
    try:
        models_summary = model_service.get_models_summary()
        
        return ModelSummaryResponse(
            success=True,
            models_summary=models_summary,
            total_model_types=len(models_summary),
            message=f"成功获取 {len(models_summary)} 个模型的摘要信息"
        )
        
    except Exception as e:
        return ModelSummaryResponse(
            success=False,
            models_summary=[],
            total_model_types=0,
            error=f"获取模型摘要失败: {str(e)}"
        )

@router.get("/{model_type}/info", response_model=ModelInfoResponse)
async def get_model_info(model_type: str):
    """
    获取指定模型类型的详细信息
    
    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)
    """
    try:
        model_info = model_service.get_model_info_by_type(model_type)
        
        if model_info is None:
            return ModelInfoResponse(
                success=False,
                error=f"未找到模型类型: {model_type}"
            )
        
        return ModelInfoResponse(
            success=True,
            model_info=model_info,
            message=f"成功获取 {model_type} 的详细信息"
        )
        
    except Exception as e:
        return ModelInfoResponse(
            success=False,
            error=f"获取模型信息失败: {str(e)}"
        )

@router.get("/{model_type}/comparison/pruning", response_model=ModelComparisonResponse)
async def get_pruning_comparison(model_type: str):
    """
    获取指定模型的剪枝比较数据
    
    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)
    """
    try:
        comparison_data = model_service.get_pruning_comparison(model_type)
        
        if comparison_data is None:
            return ModelComparisonResponse(
                success=False,
                error=f"未找到 {model_type} 的剪枝比较数据"
            )
        
        return ModelComparisonResponse(
            success=True,
            comparison_data=comparison_data,
            message=f"成功获取 {model_type} 的剪枝比较数据"
        )
        
    except Exception as e:
        return ModelComparisonResponse(
            success=False,
            error=f"获取剪枝比较数据失败: {str(e)}"
        )

@router.get("/{model_type}/comparison/distillation", response_model=ModelComparisonResponse)
async def get_distillation_comparison(model_type: str):
    """
    获取指定模型的蒸馏比较数据
    
    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)
    """
    try:
        comparison_data = model_service.get_distillation_comparison(model_type)
        
        if comparison_data is None:
            return ModelComparisonResponse(
                success=False,
                error=f"未找到 {model_type} 的蒸馏比较数据"
            )
        
        return ModelComparisonResponse(
            success=True,
            comparison_data=comparison_data,
            message=f"成功获取 {model_type} 的蒸馏比较数据"
        )
        
    except Exception as e:
        return ModelComparisonResponse(
            success=False,
            error=f"获取蒸馏比较数据失败: {str(e)}"
        )

@router.get("/{model_type}/comparison/quantization", response_model=ModelComparisonResponse)
async def get_quantization_comparison(model_type: str):
    """
    获取指定模型的量化比较数据 (仅支持ResNet50)
    
    Args:
        model_type: 模型类型，必须是 "resnet50"
    """
    try:
        if model_type != "resnet50":
            return ModelComparisonResponse(
                success=False,
                error="量化模型比较仅支持ResNet50"
            )
        
        comparison_data = model_service.get_quantization_comparison(model_type)
        
        if comparison_data is None:
            return ModelComparisonResponse(
                success=False,
                error=f"未找到 {model_type} 的量化比较数据"
            )
        
        return ModelComparisonResponse(
            success=True,
            comparison_data=comparison_data,
            message=f"成功获取 {model_type} 的量化比较数据"
        )
        
    except Exception as e:
        return ModelComparisonResponse(
            success=False,
            error=f"获取量化比较数据失败: {str(e)}"
        )

@router.get("/{model_type}/comparison", response_model=dict)
async def get_all_comparisons(model_type: str):
    """
    获取指定模型的所有可用比较数据
    
    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)
    """
    try:
        result = {
            "model_type": model_type,
            "comparisons": {}
        }
        
        # 获取剪枝比较
        pruning_data = model_service.get_pruning_comparison(model_type)
        if pruning_data:
            result["comparisons"]["pruning"] = pruning_data.dict()
        
        # 获取蒸馏比较
        distillation_data = model_service.get_distillation_comparison(model_type)
        if distillation_data:
            result["comparisons"]["distillation"] = distillation_data.dict()
        
        # 获取量化比较（仅ResNet50）
        if model_type == "resnet50":
            quantization_data = model_service.get_quantization_comparison(model_type)
            if quantization_data:
                result["comparisons"]["quantization"] = quantization_data.dict()
        
        return JSONResponse(
            content={
                "success": True,
                "data": result,
                "message": f"成功获取 {model_type} 的所有比较数据"
            }
        )
        
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "error": f"获取比较数据失败: {str(e)}"
            }
        )
