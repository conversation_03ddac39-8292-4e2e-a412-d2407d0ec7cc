import os
import torch
from models.base_model import create_model, get_model_info
from utils.metrics import count_parameters
import torch.nn as nn
from typing import Dict, Any, Optional, List, Tuple
import argparse
import traceback
import pandas as pd
import glob

def load_model(model_name:str, num_classes: int, device: str, model_path: str = None, pretrained: bool = False) -> Optional[nn.Module]:
    """
    加载指定名称的模型并返回。

    Args:
        model_name: 模型名称 (e.g., resnet50, vit_s_16)。
        num_classes: 类别数量。
        device: 使用的设备 ('cuda' 或 'cpu')。
        model_path: 模型权重路径，如果提供则从文件加载权重。
        pretrained: 是否加载预训练权重（仅当model_path为None时使用）。

    Returns:
        加载的模型，如果加载失败则返回None。
    """
    try:
        print(f"加载模型: {model_name} ", end=" ")
        if model_path:
            print(f"从路径 {model_path} ...", end=" ")
        else:
            print(f"(预训练: {pretrained}) ...", end=" ")
            
        # 使用 create_model 加载模型
        model = create_model(model_name=model_name, num_classes=num_classes, pretrained=pretrained)
        model.to(device)
        
        # 如果提供了模型路径，加载权重
        if model_path:
            state_dict = torch.load(model_path, map_location=device)
            model.load_state_dict(state_dict)
            
        model.eval()
        print("完成")
        return model
    except ImportError as ie:
        print(f"失败 (ImportError): {ie} - 请确保 timm 已安装 (pip install timm)")
        return None
    except Exception as e:
        print(f"失败: {e}")
        traceback.print_exc()
        return None

def print_comparison_results(results: List[Dict[str, Any]], output_file: Optional[str] = None) -> None:
    """
    打印并可选地保存模型参数比较结果。

    Args:
        results: 包含每个模型名称和参数量的列表字典。
        output_file: 将结果保存到的文件路径 (可选)。
    """
    if not results:
        print("没有有效的比较结果。")
        return

    # 使用 pandas 创建表格
    df = pd.DataFrame(results)
    
    # 如果存在相对值列，添加百分比格式
    if 'Relative Non-Zero Params' in df.columns:
        df['Relative Non-Zero Params'] = df['Relative Non-Zero Params'].apply(lambda x: f"{x:.2%}")
    
    # 格式化参数量，添加千位分隔符
    for col in ['Total Parameters', 'Non-Zero Parameters']:
        if col in df.columns:
            df[col] = df[col].apply(lambda x: f"{x:,}")

    # 打印到控制台
    print("\n--- 模型参数比较 ---")
    print(df.to_string(index=False))

    # 保存到文件
    if output_file:
        try:
            # 确保目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            with open(output_file, "w") as f:
                f.write(df.to_string(index=False))
            print(f"\n比较结果已保存到: {output_file}")
        except Exception as e:
            print(f"\n保存比较结果到文件时出错: {e}")

def process_run_directory(run_dir: str, model_types: List[str], num_classes: int, device: str) -> None:
    """
    处理一个运行目录，为每个模型类型比较原始模型和剪枝模型。

    Args:
        run_dir: 运行目录路径，例如 "outputs/run_20250409_141335"
        model_types: 要处理的模型类型列表，例如 ["resnet50", "densenet201", "vit_s_16", "swin_t"]
        num_classes: 模型的类别数量
        device: 使用的设备 ('cuda' 或 'cpu')
    """
    print(f"\n处理运行目录: {run_dir}")
    
    for model_type in model_types:
        print(f"\n--- 处理模型: {model_type} ---")
        
        # 构建模型路径
        model_dir = os.path.join(run_dir, model_type)
        original_model_path = os.path.join(model_dir, "model.pth")
        pruned_dir = os.path.join(model_dir, "pruned", "global_50")
        pruned_model_path = os.path.join(pruned_dir, "pruned_global_50.pth")
        
        # 检查文件是否存在
        if not os.path.exists(original_model_path):
            print(f"跳过 {model_type}: 原始模型不存在 ({original_model_path})")
            continue
            
        if not os.path.exists(pruned_model_path):
            print(f"跳过 {model_type}: 剪枝模型不存在 ({pruned_model_path})")
            continue
            
        # 创建输出目录和文件
        output_dir = os.path.join(model_dir, "pruned")
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, "parameter_comparison.txt")
        
        # 加载模型
        original_model = load_model(
            model_name=model_type,
            num_classes=num_classes,
            device=device,
            model_path=original_model_path
        )
        
        pruned_model = load_model(
            model_name=model_type,
            num_classes=num_classes,
            device=device,
            model_path=pruned_model_path
        )
        
        if original_model is None or pruned_model is None:
            print(f"跳过 {model_type} 的比较: 无法加载模型")
            continue
            
        # 计算参数数量
        original_total, original_nonzero = count_parameters(original_model)
        pruned_total, pruned_nonzero = count_parameters(pruned_model)
        
        # 计算相对值（剪枝后/原始）
        relative_nonzero = pruned_nonzero / original_nonzero if original_nonzero > 0 else 0
        
        # 构建比较结果
        results = [
            {
                "Model": f"{model_type} (Original)",
                "Total Parameters": original_total,
                "Non-Zero Parameters": original_nonzero,
                "Sparsity": 1.0 - (original_nonzero / original_total) if original_total > 0 else 0
            },
            {
                "Model": f"{model_type} (Pruned)",
                "Total Parameters": pruned_total,
                "Non-Zero Parameters": pruned_nonzero,
                "Sparsity": 1.0 - (pruned_nonzero / pruned_total) if pruned_total > 0 else 0,
                "Relative Non-Zero Params": relative_nonzero
            }
        ]
        
        # 打印和保存结果
        print_comparison_results(results, output_file)
        
        # 释放模型内存
        del original_model
        del pruned_model
        if device == 'cuda':
            torch.cuda.empty_cache()

def find_latest_run_directory(base_output_dir: str) -> Optional[str]:
    """查找最新的运行目录"""
    run_dirs = glob.glob(os.path.join(base_output_dir, "run_*"))
    if not run_dirs:
        return None
    return max(run_dirs, key=os.path.getctime)

# 主函数
def main(args):
    device = args.device if torch.cuda.is_available() and args.device == "cuda" else "cpu"
    print(f"使用设备: {device}")
    
    if args.mode == "run_directory":
        # 运行目录模式：处理整个运行目录中的所有模型
        run_dir = args.run_directory
        if not run_dir:
            # 如果未指定运行目录，尝试查找最新的运行目录
            run_dir = find_latest_run_directory(args.output_dir)
            if not run_dir:
                print(f"错误: 未指定运行目录且在 {args.output_dir} 中找不到运行目录")
                return
            print(f"使用最新的运行目录: {run_dir}")
        
        model_types = args.model_types if args.model_types else ["resnet50", "densenet201", "vit_s_16", "swin_t"]
        process_run_directory(run_dir, model_types, args.num_classes, device)
        
    else:
        # 单模型模式：比较两个特定的模型
        # 检查必要参数
        if not args.original_model_path or not args.original_model_name or not args.pruned_model_path:
            print("错误: 单模型模式需要指定 original_model_path, original_model_name, 和 pruned_model_path")
            return
            
        # 加载原始模型
        original_model = load_model(
            model_name=args.original_model_name,
            num_classes=args.num_classes,
            device=device,
            model_path=args.original_model_path
        )
        
        if not original_model:
            print("无法加载原始模型，退出")
            return
            
        # 加载剪枝模型
        pruned_model_name = args.pruned_model_name or args.original_model_name
        pruned_model = load_model(
            model_name=pruned_model_name,
            num_classes=args.num_classes,
            device=device,
            model_path=args.pruned_model_path
        )
        
        if not pruned_model:
            print("无法加载剪枝模型，退出")
            return
            
        # 计算参数数量
        original_total, original_nonzero = count_parameters(original_model)
        pruned_total, pruned_nonzero = count_parameters(pruned_model)
        
        # 计算相对值
        relative_nonzero = pruned_nonzero / original_nonzero if original_nonzero > 0 else 0
        
        # 构建比较结果
        results = [
            {
                "Model": f"{args.original_model_name} (Original)",
                "Total Parameters": original_total,
                "Non-Zero Parameters": original_nonzero,
                "Sparsity": 1.0 - (original_nonzero / original_total) if original_total > 0 else 0
            },
            {
                "Model": f"{pruned_model_name} (Pruned)",
                "Total Parameters": pruned_total,
                "Non-Zero Parameters": pruned_nonzero,
                "Sparsity": 1.0 - (pruned_nonzero / pruned_total) if pruned_total > 0 else 0,
                "Relative Non-Zero Params": relative_nonzero
            }
        ]
        
        # 打印和保存结果
        print_comparison_results(results, args.output_file)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="比较模型参数量和稀疏度")
    
    # 模式选择
    parser.add_argument("--mode", type=str, choices=["single_model", "run_directory"], default="run_directory",
                       help="比较模式: 'single_model' 比较两个特定模型, 'run_directory' 比较运行目录中的所有模型")
    
    # 运行目录模式参数
    parser.add_argument("--run_directory", type=str, default="outputs/run_20250410_111418",
                       help="运行目录路径，例如 'outputs/run_20250409_141335'")
    parser.add_argument("--model_types", type=str, nargs='+',
                       help="要处理的模型类型列表 (空格分隔)，默认为 ['resnet50', 'densenet201', 'vit_s_16', 'swin_t']")
    
    # 单模型模式参数
    parser.add_argument("--original_model_path", type=str,
                       help="原始模型的路径")
    parser.add_argument("--original_model_name", type=str,
                       help="原始模型的名称 (e.g., resnet50)")
    parser.add_argument("--pruned_model_path", type=str,
                       help="剪枝模型的路径")
    parser.add_argument("--pruned_model_name", type=str,
                       help="剪枝模型的名称 (如果与原始模型不同)")
    
    # 通用参数
    parser.add_argument("--num_classes", type=int, default=38,
                       help="模型的类别数量")
    parser.add_argument("--device", type=str, default="cuda", choices=["cuda", "cpu"],
                       help="用于加载模型的设备")
    parser.add_argument("--output_dir", type=str, default="outputs",
                       help="基础输出目录，用于自动查找最新运行目录")
    parser.add_argument("--output_file", type=str,
                       help="将比较结果保存到的文本文件路径 (单模型模式下使用)")

    args = parser.parse_args()
    main(args)
