"""
Configuration Management for Remote Sensing Image Classification System
Centralized configuration using Pydantic Settings
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any
from pydantic import BaseSettings, Field, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings


class DatabaseConfig(BaseSettings):
    """Database configuration"""
    
    # MongoDB settings
    mongodb_url: str = Field(
        default="mongodb://localhost:27017",
        env="MONGODB_URL",
        description="MongoDB connection URL"
    )
    mongodb_database: str = Field(
        default="remote_sensing_db",
        env="MONGODB_DATABASE",
        description="MongoDB database name"
    )
    
    # SQLite settings (fallback)
    sqlite_path: str = Field(
        default="data/app.db",
        env="SQLITE_PATH",
        description="SQLite database path"
    )
    
    class Config:
        env_prefix = "DB_"


class ModelConfig(BaseSettings):
    """Model configuration"""
    
    # Model paths
    models_dir: Path = Field(
        default=Path("outputs/checkpoints"),
        env="MODELS_DIR",
        description="Directory containing trained models"
    )
    
    # Default models
    default_models: Dict[str, str] = Field(
        default={
            "resnet50": "resnet50_original.pth",
            "densenet201": "densenet201_original.pth",
            "vit_s_16": "vit_s_16_original.pth",
            "swin_t": "swin_t_original.pth"
        },
        description="Default model file mappings"
    )
    
    # Model settings
    num_classes: int = Field(default=21, description="Number of classification classes")
    image_size: int = Field(default=224, description="Input image size")
    batch_size: int = Field(default=32, description="Default batch size")
    
    # Optimization settings
    enable_pruning: bool = Field(default=True, description="Enable model pruning")
    enable_quantization: bool = Field(default=True, description="Enable model quantization")
    enable_distillation: bool = Field(default=True, description="Enable knowledge distillation")
    
    class Config:
        env_prefix = "MODEL_"


class WebConfig(BaseSettings):
    """Web application configuration"""
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="HOST", description="Server host")
    port: int = Field(default=8000, env="PORT", description="Server port")
    debug: bool = Field(default=False, env="DEBUG", description="Debug mode")
    reload: bool = Field(default=False, env="RELOAD", description="Auto-reload on changes")
    
    # Security settings
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY",
        description="Secret key for sessions"
    )
    allowed_hosts: List[str] = Field(
        default=["*"],
        env="ALLOWED_HOSTS",
        description="Allowed hosts for CORS"
    )
    
    # File upload settings
    max_file_size: int = Field(
        default=10 * 1024 * 1024,  # 10MB
        env="MAX_FILE_SIZE",
        description="Maximum file upload size in bytes"
    )
    allowed_extensions: List[str] = Field(
        default=[".jpg", ".jpeg", ".png", ".webp"],
        description="Allowed file extensions"
    )
    
    # Static files
    static_dir: Path = Field(
        default=Path("static"),
        env="STATIC_DIR",
        description="Static files directory"
    )
    
    class Config:
        env_prefix = "WEB_"


class LoggingConfig(BaseSettings):
    """Logging configuration"""
    
    # Log levels
    log_level: str = Field(default="INFO", env="LOG_LEVEL", description="Logging level")
    log_format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        env="LOG_FORMAT",
        description="Log message format"
    )
    
    # Log files
    log_dir: Path = Field(
        default=Path("logs"),
        env="LOG_DIR",
        description="Log files directory"
    )
    log_file: str = Field(
        default="app.log",
        env="LOG_FILE",
        description="Main log file name"
    )
    
    # Log rotation
    log_rotation: str = Field(
        default="1 day",
        env="LOG_ROTATION",
        description="Log rotation interval"
    )
    log_retention: str = Field(
        default="30 days",
        env="LOG_RETENTION",
        description="Log retention period"
    )
    
    class Config:
        env_prefix = "LOG_"


class MonitoringConfig(BaseSettings):
    """Monitoring and analytics configuration"""
    
    # Weights & Biases
    wandb_project: Optional[str] = Field(
        default=None,
        env="WANDB_PROJECT",
        description="Weights & Biases project name"
    )
    wandb_entity: Optional[str] = Field(
        default=None,
        env="WANDB_ENTITY",
        description="Weights & Biases entity name"
    )
    
    # MLflow
    mlflow_tracking_uri: Optional[str] = Field(
        default=None,
        env="MLFLOW_TRACKING_URI",
        description="MLflow tracking server URI"
    )
    mlflow_experiment_name: str = Field(
        default="remote_sensing_classification",
        env="MLFLOW_EXPERIMENT_NAME",
        description="MLflow experiment name"
    )
    
    # TensorBoard
    tensorboard_log_dir: Path = Field(
        default=Path("logs/tensorboard"),
        env="TENSORBOARD_LOG_DIR",
        description="TensorBoard log directory"
    )
    
    # Performance monitoring
    enable_performance_monitoring: bool = Field(
        default=True,
        description="Enable performance monitoring"
    )
    
    class Config:
        env_prefix = "MONITORING_"


class DataConfig(BaseSettings):
    """Data configuration"""
    
    # Data directories
    data_dir: Path = Field(
        default=Path("data"),
        env="DATA_DIR",
        description="Main data directory"
    )
    dataset_dir: Path = Field(
        default=Path("data/UC_Merced"),
        env="DATASET_DIR",
        description="Dataset directory"
    )
    
    # Data processing
    train_split: float = Field(
        default=0.8,
        env="TRAIN_SPLIT",
        description="Training data split ratio"
    )
    val_split: float = Field(
        default=0.1,
        env="VAL_SPLIT",
        description="Validation data split ratio"
    )
    test_split: float = Field(
        default=0.1,
        env="TEST_SPLIT",
        description="Test data split ratio"
    )
    
    # Data augmentation
    enable_augmentation: bool = Field(
        default=True,
        description="Enable data augmentation"
    )
    
    @validator('train_split', 'val_split', 'test_split')
    def validate_splits(cls, v):
        if not 0 < v < 1:
            raise ValueError('Split ratios must be between 0 and 1')
        return v
    
    class Config:
        env_prefix = "DATA_"


class AppConfig(PydanticBaseSettings):
    """Main application configuration"""
    
    # Application info
    app_name: str = Field(
        default="Remote Sensing Image Classification System",
        description="Application name"
    )
    app_version: str = Field(default="0.1.0", description="Application version")
    app_description: str = Field(
        default="Remote Sensing Image Classification with Model Optimization",
        description="Application description"
    )
    
    # Environment
    environment: str = Field(
        default="development",
        env="ENVIRONMENT",
        description="Application environment (development/production/testing)"
    )
    
    # Sub-configurations
    database: DatabaseConfig = DatabaseConfig()
    model: ModelConfig = ModelConfig()
    web: WebConfig = WebConfig()
    logging: LoggingConfig = LoggingConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    data: DataConfig = DataConfig()
    
    # Feature flags
    enable_gradio: bool = Field(default=True, description="Enable Gradio interface")
    enable_web_ui: bool = Field(default=True, description="Enable web UI")
    enable_api: bool = Field(default=True, description="Enable API endpoints")
    enable_monitoring: bool = Field(default=True, description="Enable monitoring")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            self.model.models_dir,
            self.logging.log_dir,
            self.monitoring.tensorboard_log_dir,
            self.data.data_dir,
            self.data.dataset_dir,
            self.web.static_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.environment.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return self.environment.lower() == "production"
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing mode"""
        return self.environment.lower() == "testing"


# Global configuration instance
config = AppConfig()

# Create necessary directories on import
config.create_directories()


def get_config() -> AppConfig:
    """Get the global configuration instance"""
    return config


def reload_config() -> AppConfig:
    """Reload configuration from environment"""
    global config
    config = AppConfig()
    config.create_directories()
    return config


# Export commonly used configurations
__all__ = [
    "AppConfig",
    "DatabaseConfig", 
    "ModelConfig",
    "WebConfig",
    "LoggingConfig",
    "MonitoringConfig",
    "DataConfig",
    "config",
    "get_config",
    "reload_config",
]
