"""
数据集相关的Pydantic模型定义

包含数据集管理和图片展示的数据模型
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class ImageInfo(BaseModel):
    """图片信息"""
    path: str = Field(..., description="图片路径")
    filename: str = Field(..., description="文件名")
    class_name: str = Field(..., description="类别名称")
    size_bytes: Optional[int] = Field(None, description="文件大小(字节)")
    base64_data: Optional[str] = Field(None, description="Base64编码的图片数据")

class DatasetSampleRequest(BaseModel):
    """数据集示例请求"""
    dataset_type: str = Field("old", description="数据集类型 (old/new)")
    num_classes: int = Field(3, ge=1, le=10, description="选择的类别数量")
    num_images_per_class: int = Field(3, ge=1, le=10, description="每个类别选择的图片数量")
    include_base64: bool = Field(False, description="是否包含Base64编码的图片数据")

class DatasetSampleResponse(BaseModel):
    """数据集示例响应"""
    success: bool = Field(..., description="请求是否成功")
    dataset_type: str = Field(..., description="数据集类型")
    images: List[ImageInfo] = Field(..., description="图片信息列表")
    total_images: int = Field(..., description="图片总数")
    classes_found: List[str] = Field(..., description="找到的类别列表")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")

class DatasetComparisonRequest(BaseModel):
    """数据集对比请求"""
    num_images_per_dataset: int = Field(4, ge=1, le=10, description="每个数据集选择的图片数量")
    include_base64: bool = Field(False, description="是否包含Base64编码的图片数据")
    specific_class: Optional[str] = Field(None, description="指定对比的类别名称")

class DatasetComparisonResponse(BaseModel):
    """数据集对比响应"""
    success: bool = Field(..., description="请求是否成功")
    old_dataset_images: List[ImageInfo] = Field(..., description="旧数据集图片")
    new_dataset_images: List[ImageInfo] = Field(..., description="新数据集图片")
    comparison_class: Optional[str] = Field(None, description="对比的类别名称")
    old_dataset_count: int = Field(..., description="旧数据集图片数量")
    new_dataset_count: int = Field(..., description="新数据集图片数量")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")

class DatasetStatsResponse(BaseModel):
    """数据集统计响应"""
    success: bool = Field(..., description="请求是否成功")
    old_dataset_stats: Dict[str, Any] = Field(..., description="旧数据集统计")
    new_dataset_stats: Dict[str, Any] = Field(..., description="新数据集统计")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")

class DatasetClassInfo(BaseModel):
    """数据集类别信息"""
    class_name: str = Field(..., description="类别名称")
    image_count: int = Field(..., description="图片数量")
    sample_images: List[str] = Field(..., description="示例图片路径")

class DatasetInfo(BaseModel):
    """数据集信息"""
    dataset_path: str = Field(..., description="数据集路径")
    exists: bool = Field(..., description="数据集是否存在")
    total_classes: int = Field(..., description="总类别数")
    total_images: int = Field(..., description="总图片数")
    classes: List[DatasetClassInfo] = Field(..., description="类别详细信息")

class DatasetInfoResponse(BaseModel):
    """数据集信息响应"""
    success: bool = Field(..., description="请求是否成功")
    dataset_info: Optional[DatasetInfo] = Field(None, description="数据集信息")
    message: Optional[str] = Field(None, description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")
