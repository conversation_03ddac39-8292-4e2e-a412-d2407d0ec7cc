import os
import argparse
import time
from typing import Dict, Tuple
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm

from data.dataset import create_dataloaders
from models.base_model import create_model, get_model_info
from optimization.quantization import (
    quantize_for_cuda,
    get_size_of_model, compare_models_size
)
from utils.metrics import evaluate_model, count_parameters
from utils.visualization import plot_model_comparison


def evaluate_quantized_model(
    original_model: nn.Module,
    quantized_model: nn.<PERSON><PERSON>le,
    test_loader: DataLoader,
    device: torch.device,
    classes: list,
    output_dir: str,
    model_name: str
) -> Dict:
    """
    评估量化模型性能
    
    Args:
        original_model: 原始模型
        quantized_model: 量化后的模型
        test_loader: 测试数据加载器
        device: 计算设备
        classes: 类别列表
        output_dir: 输出目录
        model_name: 模型名称
        
    Returns:
        性能比较结果
    """
    # 确保模型在评估模式
    original_model.eval()
    quantized_model.eval()
    
    # 评估原始模型
    print("评估原始模型...")
    original_metrics = evaluate_model(original_model, test_loader, device, classes)
    print(f"原始模型准确率: {original_metrics['accuracy']:.4f}")
    
    # 评估量化模型
    print("评估量化模型...")
    quantized_metrics = evaluate_model(quantized_model, test_loader, device, classes)
    print(f"量化模型准确率: {quantized_metrics['accuracy']:.4f}")
    
    # # 测量推理时间
    # print("测量推理时间...")
    # batch_size = test_loader.batch_size
    # dummy_input = torch.randn(batch_size, 3, 224, 224, device=device)
    
    # # 原始模型推理时间
    # original_model.to(device)
    # # 预热
    # for _ in range(10):
    #     _ = original_model(dummy_input)
    
    # # 计时
    # torch.cuda.synchronize()
    # start_time = time.time()
    # iterations = 100
    # for _ in range(iterations):
    #     _ = original_model(dummy_input)
    # torch.cuda.synchronize()
    # end_time = time.time()
    
    # original_inference_time = (end_time - start_time) / iterations
    
    # # 量化模型推理时间
    # quantized_model.to(device)
    # # 预热
    # for _ in range(10):
    #     _ = quantized_model(dummy_input)
    
    # # 计时
    # torch.cuda.synchronize()
    # start_time = time.time()
    # for _ in range(iterations):
    #     _ = quantized_model(dummy_input)
    # torch.cuda.synchronize()
    # end_time = time.time()
    
    # quantized_inference_time = (end_time - start_time) / iterations
    
    # 比较模型大小
    original_size = get_size_of_model(original_model, os.path.join(output_dir, "temp_original.pt"))
    quantized_size = get_size_of_model(quantized_model, os.path.join(output_dir, "temp_quantized.pt"))
    
    size_comparison = compare_models_size(original_model, quantized_model)
    
    # 输出结果
    print(f"原始模型大小: {original_size / (1024*1024):.2f} MB")
    print(f"量化模型大小: {quantized_size / (1024*1024):.2f} MB")
    print(f"大小减少比例: {size_comparison['size_reduction_percentage']:.2f}%")
    
    # print(f"原始模型推理时间: {original_inference_time*1000:.2f} ms")
    # print(f"量化模型推理时间: {quantized_inference_time*1000:.2f} ms")
    # print(f"速度提升: {original_inference_time/quantized_inference_time:.2f}x")
    
    # 保存量化模型
    model_path = os.path.join(output_dir, f"quantized_{model_name}.pth")
    torch.save(quantized_model.state_dict(), model_path)
    print(f"量化模型已保存到: {model_path}")
    
    # 汇总结果
    results = {
        "model_name": model_name,
        "original_accuracy": original_metrics["accuracy"],
        "quantized_accuracy": quantized_metrics["accuracy"],
        "accuracy_drop": original_metrics["accuracy"] - quantized_metrics["accuracy"],
        "relative_accuracy": quantized_metrics["accuracy"] / original_metrics["accuracy"],
        "original_size_mb": original_size / (1024*1024),
        "quantized_size_mb": quantized_size / (1024*1024),
        "size_reduction": size_comparison["size_reduction_ratio"],
        "original_inference_time_ms": 0,
        "quantized_inference_time_ms": 0,
        "speedup": 0
    }
    
    return results


def main(args):
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载数据
    train_loader, val_loader, test_loader, classes = create_dataloaders(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers
    )
    
    # 加载原始模型
    print(f"加载原始模型: {args.model_path}")
    model = create_model(num_classes=len(classes), pretrained=False)
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model = model.to(device)
    
    # 存储结果
    all_results = []
    
    # CPU兼容量化
    print("\n正在进行CPU兼容量化...")
    try:
        # 确保模型在CPU上
        model_cpu = model.cpu()  
        quantized_model = quantize_for_cuda(model_cpu)
        
        # 评估量化模型
        print("在CPU上评估量化模型...")
        cpu_device = torch.device("cpu")
        results = evaluate_quantized_model(
            original_model=model.to(cpu_device),
            quantized_model=quantized_model,
            test_loader=test_loader,
            device=cpu_device,
            classes=classes,
            output_dir=args.output_dir,
            model_name="cpu_quantized"
        )
        all_results.append(results)
    except Exception as e:
        print(f"量化失败: {e}")
        print("请确保模型可以在CPU上运行并支持量化操作")
    
    # 比较和可视化
    if len(all_results) > 0:
        # 提取对比数据
        model_names = ["original"] + [r["model_name"] for r in all_results]
        accuracies = [1.0] + [r["relative_accuracy"] * 100 for r in all_results]
        file_sizes = [all_results[0]["original_size_mb"]] + [r["quantized_size_mb"] for r in all_results]
        inference_times = [all_results[0]["original_inference_time_ms"]] + [r["quantized_inference_time_ms"] for r in all_results]
        
        # 绘制比较图
        comparison_path = os.path.join(args.output_dir, "quantization_comparison.png")
        plot_model_comparison(
            model_names, 
            accuracies, 
            file_sizes, 
            inference_times, 
            save_path=comparison_path
        )
        print(f"量化比较图已保存到: {comparison_path}")
    
    print("量化完成！")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="模型量化")
    
    # 数据参数
    parser.add_argument("--data_dir", type=str, default="PatternNet/images", help="数据集目录")
    parser.add_argument("--batch_size", type=int, default=32, help="批量大小")
    
    # 模型参数
    parser.add_argument("--model_path", type=str, default="outputs/model.pth", help="原始模型路径")
    
    # 硬件参数
    parser.add_argument("--device", type=str, default="cuda", help="训练设备")
    parser.add_argument("--num_workers", type=int, default=4, help="数据加载线程数")
    
    # 输出参数
    parser.add_argument(
        "--output_dir", type=str, default="outputs\quantized", help="量化模型输出目录"
    )
    
    args = parser.parse_args()
    
    main(args) 