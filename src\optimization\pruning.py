import torch
import torch.nn as nn
import torch.nn.utils.prune as prune
from typing import Dict, List, Tuple, Optional, Callable
import copy


def get_prunable_layers(
    model: nn.Module,
    include_linear=True,
    include_conv=True,
    prune_bias=False,  # 通常不建议剪枝 bias，但提供选项
) -> List[Tuple[nn.Module, str]]:
    """
    获取模型中可以被剪枝的层 (优化版，包含 Conv 和 Linear)

    Args:
        model: PyTorch模型
        include_linear: 是否包含 nn.Linear 层
        include_conv: 是否包含 nn.Conv2d 层
        prune_bias: 是否剪枝 bias 参数

    Returns:
        可剪枝层的(模块, 参数名称 'weight' 或 'bias')列表
    """
    prunable_layers = []
    for module in model.modules():  # 遍历所有模块，包括嵌套的
        if (include_conv and isinstance(module, nn.Conv2d)) or (
            include_linear and isinstance(module, nn.Linear)
        ):
            # 剪枝 weight
            if hasattr(module, "weight") and module.weight is not None:
                # 检查是否已经被添加了剪枝重参数化（避免重复添加）
                if not prune.is_pruned(module):  # 或者检查 module._forward_pre_hooks
                    prunable_layers.append((module, "weight"))
                else:
                    # 如果已经有剪枝 mask，理论上 global_unstructured 会处理
                    # 但为了清晰，最好在应用新剪枝前移除旧的（如果需要多次剪枝）
                    # 这里假设只剪一次
                    if ("weight_orig" in dict(module.named_parameters())) and (
                        "weight_mask" in dict(module.named_buffers())
                    ):
                        prunable_layers.append(
                            (module, "weight")
                        )  # 允许对已剪枝层再次操作

            # （可选）剪枝 bias
            if prune_bias and hasattr(module, "bias") and module.bias is not None:
                # 同样检查是否已剪枝
                if not prune.is_pruned(module) or (
                    "bias_orig" in dict(module.named_parameters())
                ):
                    prunable_layers.append((module, "bias"))

    # 去重（虽然理论上按模块遍历不会重复添加同一模块的同一参数，但保险起见）
    # 注意：直接用 set 去重 Tuple[nn.Module, str] 可能因为 nn.Module 不可哈希而出错
    # 简单的去重可以通过维护一个 seen 集合
    seen = set()
    unique_prunable_layers = []
    for module, name in prunable_layers:
        # 使用 module 的 id 和 name 作为唯一标识符
        identifier = (id(module), name)
        if identifier not in seen:
            unique_prunable_layers.append((module, name))
            seen.add(identifier)

    if not unique_prunable_layers:
        print(
            "警告: 在模型中没有找到符合条件的可剪枝层 (Conv2d/Linear)。请检查模型结构和函数参数。"
        )

    return unique_prunable_layers


def prune_model_l1_unstructured(
    model: nn.Module, 
    pruning_ratio: float
) -> nn.Module:
    """
    使用L1范数进行非结构化剪枝
    
    Args:
        model: 待剪枝的PyTorch模型
        pruning_ratio: 剪枝比例，0-1之间的值
        
    Returns:
        剪枝后的模型
    """
    # 复制模型，避免修改原始模型
    pruned_model = copy.deepcopy(model)
    
    # 获取可剪枝的层
    prunable_layers = get_prunable_layers(pruned_model)
    
    # 对每一层进行剪枝
    for module, name in prunable_layers:
        prune.l1_unstructured(module, name=name, amount=pruning_ratio)
        # 使剪枝永久化
        prune.remove(module, name)
    
    return pruned_model


def prune_model_l1_structured(
    model: nn.Module, 
    pruning_ratio: float
) -> nn.Module:
    """
    使用L1范数进行结构化剪枝（按通道剪枝）
    
    Args:
        model: 待剪枝的PyTorch模型
        pruning_ratio: 剪枝比例，0-1之间的值
        
    Returns:
        剪枝后的模型
    """
    # 复制模型，避免修改原始模型
    pruned_model = copy.deepcopy(model)
    
    # 对卷积层进行剪枝
    for name, module in pruned_model.named_modules():
        if isinstance(module, nn.Conv2d):
            prune.ln_structured(module, name='weight', amount=pruning_ratio, n=1, dim=0)
            # 使剪枝永久化
            prune.remove(module, name='weight')
    
    return pruned_model


def prune_model_global(
    model: nn.Module,
    pruning_ratio: float,
    include_linear=True,
    include_conv=True,
    prune_bias=False,
) -> nn.Module:
    """
    全局非结构化剪枝 (优化版)

    Args:
        model: 待剪枝的PyTorch模型
        pruning_ratio: 剪枝比例 (作用于被选中的参数)，0-1之间的值
        include_linear: 是否剪枝 nn.Linear 层
        include_conv: 是否剪枝 nn.Conv2d 层
        prune_bias: 是否剪枝 bias 参数

    Returns:
        剪枝后的模型 (剪枝已永久化)
    """
    print("--- 开始全局剪枝 ---")
    pruned_model = copy.deepcopy(model)  # 复制模型

    # --- 1. 获取可剪枝层 ---
    print(
        f"查找可剪枝层 (Conv={'是' if include_conv else '否'}, Linear={'是' if include_linear else '否'}, Bias={'是' if prune_bias else '否'})..."
    )
    prunable_layers = get_prunable_layers(
        pruned_model, include_linear, include_conv, prune_bias
    )

    if not prunable_layers:
        print("没有找到可剪枝的层，剪枝过程终止。返回原始模型的副本。")
        return pruned_model

    print(f"找到 {len(prunable_layers)} 个可剪枝的参数组 (module, param_name)。")

    # --- 2. 应用全局非结构化剪枝 ---
    print(f"\n应用全局非结构化 L1 剪枝，目标比例: {pruning_ratio:.2%}...")
    try:
        prune.global_unstructured(
            prunable_layers, 
            pruning_method=prune.L1Unstructured,
            amount=pruning_ratio,  
        )
    except Exception as e:
        print(f"\n错误: 在应用 prune.global_unstructured 时发生异常: {e}")
        raise e  

    # --- 4. 计算应用剪枝（但未移除 mask）后的非零参数量 ---

    nonzero_params_after_mask = 0
    params_pruned_in_scope = 0
    total_params_in_scope_after_mask = 0  

    for module, name in prunable_layers:
        # 检查 mask 是否存在
        mask_name = f"{name}_mask"
        if hasattr(module, mask_name):
            mask = getattr(module, mask_name)
            orig_param_name = f"{name}_orig"
            if hasattr(module, orig_param_name):
                original_param = getattr(module, orig_param_name)
                effective_param = original_param * mask  # 应用 mask
                nonzero_params_after_mask += torch.count_nonzero(effective_param).item()
                total_params_in_scope_after_mask += original_param.numel()
                params_pruned_in_scope += (
                    original_param.numel() - torch.count_nonzero(effective_param).item()
                )

        else:
            # 如果没有 mask (例如 bias 可能没有被剪枝)，直接加现有非零参数
            param = getattr(module, name)
            nonzero_params_after_mask += torch.count_nonzero(param).item()
            total_params_in_scope_after_mask += param.numel()


    print("\n应用剪枝 Mask 后 (理论值):")
    print(f"  被剪枝参数范围内的非零参数量: {nonzero_params_after_mask:,}")


    # --- 5. 使剪枝永久化  ---
    print("\n使剪枝永久化 (移除 masks)...")

    for module, name in prunable_layers:
        try:

            if prune.is_pruned(module):  # 更可靠的检查方式
                # 检查特定参数是否被剪枝（虽然 global 会应用到所有）
                is_param_pruned = f"{name}_orig" in dict(module.named_parameters())
                if is_param_pruned:
                    prune.remove(module, name)


        except ValueError as ve:
            # 捕获尝试移除未剪枝参数时可能出现的错误
            print(
                f"  警告: 尝试移除 {module.__class__.__name__} 的 '{name}' 参数时出错: {ve}。可能该参数未被成功应用剪枝。"
            )
        except Exception as e:
            print(
                f"  错误: 在移除 {module.__class__.__name__} 的 '{name}' 参数剪枝时发生未知错误: {e}"
            )
            # 可能需要更详细的调试

    print("--- 剪枝完成 ---")

    return pruned_model


def iterative_pruning(
    model: nn.Module,
    pruning_ratio: float,
    steps: int,
    prune_func: Callable,
    fine_tune_func: Optional[Callable] = None
) -> nn.Module:
    """
    迭代剪枝，逐步提高剪枝强度
    
    Args:
        model: 待剪枝的PyTorch模型
        pruning_ratio: 最终剪枝比例，0-1之间的值
        steps: 迭代步数
        prune_func: 单步剪枝函数
        fine_tune_func: 每步剪枝后的微调函数，如果为None则不进行微调
        
    Returns:
        剪枝后的模型
    """
    # 复制模型，避免修改原始模型
    current_model = copy.deepcopy(model)
    
    # 计算每步的剪枝比例
    step_ratio = pruning_ratio / steps
    
    # 迭代剪枝
    for i in range(steps):
        print(f"剪枝步骤 {i+1}/{steps}, 比例: {step_ratio:.4f}")
        current_model = prune_func(current_model, step_ratio)
        
        # 如果提供了微调函数，进行微调
        if fine_tune_func is not None:
            current_model = fine_tune_func(current_model)
    
    return current_model


def calculate_sparsity(model: nn.Module) -> float:
    """
    计算模型的稀疏度（零参数比例）
    
    Args:
        model: PyTorch模型
        
    Returns:
        稀疏度，0-1之间的值
    """
    total_params = 0
    zero_params = 0
    
    for name, param in model.named_parameters():
        if 'weight' in name:
            total_params += param.numel()
            zero_params += torch.sum(param == 0).item()
    
    return zero_params / total_params if total_params > 0 else 0.0


def compare_models(original_model: nn.Module, pruned_model: nn.Module) -> Dict:
    """
    比较原始模型和剪枝后的模型
    
    Args:
        original_model: 原始模型
        pruned_model: 剪枝后的模型
        
    Returns:
        包含比较结果的字典
    """
    # 计算原始模型参数量
    original_params = sum(p.numel() for p in original_model.parameters() if p.requires_grad)
    
    # 计算剪枝后模型参数量
    pruned_params = sum(p.numel() for p in pruned_model.parameters() if p.requires_grad)
    
    # 计算剪枝后模型非零参数量
    pruned_nonzero_params = sum((p != 0).sum().item() for name, p in pruned_model.named_parameters() if 'weight' in name)
    
    # 计算稀疏度
    sparsity = calculate_sparsity(pruned_model)
    
    # 计算参数减少比例
    param_reduction = 1 - pruned_nonzero_params / original_params
    
    return {
        "original_params": original_params,
        "pruned_params": pruned_params,
        "pruned_nonzero_params": pruned_nonzero_params,
        "sparsity": sparsity,
        "param_reduction": param_reduction
    }


if __name__ == "__main__":
    # 测试代码
    from torchvision.models import resnet18
    
    # 创建测试模型
    model = resnet18(pretrained=True)
    print(f"原始模型参数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # L1非结构化剪枝
    pruned_unstructured = prune_model_l1_unstructured(model, 0.3)
    print(f"非结构化剪枝后稀疏度: {calculate_sparsity(pruned_unstructured):.4f}")
    
    # L1结构化剪枝
    pruned_structured = prune_model_l1_structured(model, 0.3)
    print(f"结构化剪枝后稀疏度: {calculate_sparsity(pruned_structured):.4f}")
    
    # 全局剪枝
    pruned_global = prune_model_global(model, 0.3)
    print(f"全局剪枝后稀疏度: {calculate_sparsity(pruned_global):.4f}")
    
    # 比较结果
    comparison = compare_models(model, pruned_global)
    for key, value in comparison.items():
        print(f"{key}: {value}") 