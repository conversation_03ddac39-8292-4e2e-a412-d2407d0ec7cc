/**
 * Service Worker for Remote Sensing Image Classification System
 * Provides offline support and caching functionality
 */

const CACHE_NAME = 'rsic-cache-v1';
const STATIC_CACHE_NAME = 'rsic-static-v1';
const DYNAMIC_CACHE_NAME = 'rsic-dynamic-v1';

// Files to cache for offline use
const STATIC_FILES = [
    '/',
    '/static/css/style.css',
    '/static/js/main.js',
    '/static/index.html',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
    '/api/models',
    '/api/health'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then((cache) => {
                console.log('Caching static files...');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Static files cached successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Failed to cache static files:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== DYNAMIC_CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle requests
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle different types of requests
    if (request.method === 'GET') {
        if (isStaticFile(url.pathname)) {
            // Static files - cache first strategy
            event.respondWith(cacheFirst(request));
        } else if (isAPIRequest(url.pathname)) {
            // API requests - network first strategy
            event.respondWith(networkFirst(request));
        } else if (isImageRequest(url.pathname)) {
            // Images - cache first strategy
            event.respondWith(cacheFirst(request));
        } else {
            // Other requests - network first strategy
            event.respondWith(networkFirst(request));
        }
    }
});

// Cache first strategy - for static files
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Cache first strategy failed:', error);
        return getOfflineFallback(request);
    }
}

// Network first strategy - for API requests
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful API responses
            if (isAPIRequest(new URL(request.url).pathname)) {
                const cache = await caches.open(DYNAMIC_CACHE_NAME);
                cache.put(request, networkResponse.clone());
            }
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network request failed, trying cache:', error);
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return getOfflineFallback(request);
    }
}

// Check if request is for static file
function isStaticFile(pathname) {
    return pathname.startsWith('/static/') || 
           pathname === '/' || 
           pathname.endsWith('.css') || 
           pathname.endsWith('.js') || 
           pathname.endsWith('.html');
}

// Check if request is for API
function isAPIRequest(pathname) {
    return pathname.startsWith('/api/') || 
           API_CACHE_PATTERNS.some(pattern => pathname.includes(pattern));
}

// Check if request is for image
function isImageRequest(pathname) {
    return pathname.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i) ||
           pathname.includes('/api/dataset/image/');
}

// Get offline fallback response
function getOfflineFallback(request) {
    const url = new URL(request.url);
    
    if (request.destination === 'document') {
        // Return cached main page for navigation requests
        return caches.match('/');
    }
    
    if (isAPIRequest(url.pathname)) {
        // Return offline API response
        return new Response(
            JSON.stringify({
                error: 'Offline',
                message: '当前处于离线状态，请检查网络连接'
            }),
            {
                status: 503,
                statusText: 'Service Unavailable',
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
    }
    
    if (isImageRequest(url.pathname)) {
        // Return placeholder for images
        return new Response(
            '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">' +
            '<rect width="200" height="200" fill="#f0f0f0"/>' +
            '<text x="100" y="100" text-anchor="middle" dy=".3em" fill="#999">离线模式</text>' +
            '</svg>',
            {
                headers: {
                    'Content-Type': 'image/svg+xml'
                }
            }
        );
    }
    
    // Default offline response
    return new Response(
        '离线模式 - 请检查网络连接',
        {
            status: 503,
            statusText: 'Service Unavailable',
            headers: {
                'Content-Type': 'text/plain'
            }
        }
    );
}

// Message handling for cache management
self.addEventListener('message', (event) => {
    const { action, data } = event.data;
    
    switch (action) {
        case 'CLEAR_CACHE':
            clearCache(data.cacheName);
            break;
        case 'UPDATE_CACHE':
            updateCache(data.urls);
            break;
        case 'GET_CACHE_STATUS':
            getCacheStatus().then(status => {
                event.ports[0].postMessage(status);
            });
            break;
        default:
            console.log('Unknown message action:', action);
    }
});

// Clear specific cache
async function clearCache(cacheName) {
    try {
        const deleted = await caches.delete(cacheName || DYNAMIC_CACHE_NAME);
        console.log('Cache cleared:', cacheName, deleted);
    } catch (error) {
        console.error('Failed to clear cache:', error);
    }
}

// Update cache with new URLs
async function updateCache(urls) {
    try {
        const cache = await caches.open(DYNAMIC_CACHE_NAME);
        await cache.addAll(urls);
        console.log('Cache updated with new URLs:', urls);
    } catch (error) {
        console.error('Failed to update cache:', error);
    }
}

// Get cache status
async function getCacheStatus() {
    try {
        const cacheNames = await caches.keys();
        const status = {};
        
        for (const cacheName of cacheNames) {
            const cache = await caches.open(cacheName);
            const keys = await cache.keys();
            status[cacheName] = keys.length;
        }
        
        return status;
    } catch (error) {
        console.error('Failed to get cache status:', error);
        return {};
    }
}

// Background sync for failed requests
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    try {
        // Retry failed API requests
        console.log('Performing background sync...');
        
        // This would typically retry failed requests stored in IndexedDB
        // For now, just log the sync event
        console.log('Background sync completed');
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Push notification handling
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body || '您有新的通知',
            icon: '/static/icon-192.png',
            badge: '/static/badge-72.png',
            tag: data.tag || 'default',
            data: data.data || {},
            actions: data.actions || []
        };
        
        event.waitUntil(
            self.registration.showNotification(
                data.title || '遥感图像分类系统',
                options
            )
        );
    }
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    if (event.action) {
        // Handle action button clicks
        console.log('Notification action clicked:', event.action);
    } else {
        // Handle notification click
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

console.log('Service Worker script loaded');
